# OS
.DS_Store
Thumbs.db

# Build outputs
/build/
/bin/
/obj/
*.obj
*.pdb
*.idb
*.ilk
*.exe
*.dll
*.lib
*.exp
*.log
*.tlog
*.ipdb
*.iobj
*.user
*.vc.db
*.VC.db
*.opendb
*.cache
*.suo
*.lastbuildstate
*.log
*.mdb
*.pch
*.spf

# Visual Studio
.vs/
*.vcxproj.user
*.VC.opendb
*.VC.VC.opendb

# CMake (if used later)
CMakeFiles/
CMakeCache.txt
cmake-build*/

# Python venv (if used later)
.venv/



# Visual Studio build dirs
/x64/
/x86/
/ARM/
/ARM64/
/Release/
/Debug/
/ipch/

# VS database and intermediates
*.sdf
*.opensdf
*.sqlite
*.sbr
*.bsc
*.rsp
*.tmp
*.bak
*.old
*.recipe

# IDE settings
.vscode/
.idea/

# Python caches & envs (WeltDetect)
__pycache__/
*.pyc
*.pyo
.pytest_cache/
.mypy_cache/
.ruff_cache/
.ipynb_checkpoints/
.venv/
env/
.conda/

# Data & outputs
data/
WeltDetect/data/output/
WeltDetect/data/processed/
*.raw

# Models / large binaries (keep out of repo)
WeltDetect/models/
*.pt
*.onnx

# Misc OS noise
desktop.ini
$RECYCLE.BIN/

*.zip
交付文件汇总*