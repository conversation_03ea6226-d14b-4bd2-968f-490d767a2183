# SphereSeg3D 开发总览与对齐要求（WeltDetect → C++ 迁移）

更新时间：2025-08-15

## 目标
- 将 Python 工程 WeltDetect 的实现完整迁移到 SphereSeg3D C++工程，其中SphereSeg3DCpp是一个DLL工程，SphereSeg3DApp是一个可执行工程，用于演示调用和测试DLL模块。
- Ｃ++侧和Python 侧的模块与函数要一一对应（可直译函数名与参数，但可以改成适合Ｃ＋＋的命名规则，比如驼峰命名），保证后续 Python 侧更新能快速同步到 C++。
- 检测、分割、日志与调试输出与 Python 结果一致，最终测试结果一致。
- 方案内不改动 Detector.cpp/h（推理层）

## 模块映射（Python → C++）
- WeltDetect/src/data_loader.py → SphereSeg3D::load_volume_from_slices
- WeltDetect/src/auto_focus.py → SphereSeg3D::auto_focus（Tenengrad+AMPD）
- WeltDetect/src/ai_inference.py → Detector.h/Detector.cpp (YoloOnnxDetector) + SphereSeg3D::detect_objects
- WeltDetect/main.py 倾斜校正与拉平 → SphereSeg3D::apply_tilt_correction
- WeltDetect/src/sphere_segmentation.py → SphereSeg3D::segment_spheres_binary（类别0/1/2/3策略、自适应阈值、球/柱约束）
- WeltDetect/src/sphere_segmentation.py 保存 → SphereSeg3D::save_3d_segmentation_result_with_rand_bg（随机背景、简单膨胀）
- SphereSeg3D\x64\Release\Config\bestV5.onnx → WeltDetect\models\bestV5.pt(这两个模型文件是等价的)

## 主线流程
- App入口(SphereSeg3DApp/main.cpp) → SphereSeg3D::process_volume：
  1) load_volume_from_slices: 解析 ImageParam.ini（section/NameTemplate/BeginIndex/EndIndex、dtype），加载体数据
  2) auto_focus: Tenengrad 评分 + AMPD 峰检测
  3) detect_objects: 在峰列表各层推理，选“检测数最多，其次聚焦分最高”的层
  4) apply_tilt_correction: 角点采样→平面拟合→网格拉平→马赛克复检→为每框赋 z
  5) filter_by_class3_bigbox: 合成类3大框，过滤中心不在大框内的其它框，并更新类3为合并外接框
  6) segment_spheres_binary: 类0/2球形约束、类1柱状约束，自适应阈值二值化
  7) save_3d_segmentation_result_with_rand_bg: 合并mask、简单膨胀、随机背景填充并保存 RAW 与 focus PNG


## 设计与约束
- Detector.cpp/h 为其他人开发，不改，直接调用；
- C++ 接口与 Python 一一对应，便于逐行对照；
- 文件编码与风格：
  - 源码：UTF-8，CRLF，Allman 大括号风格；一行一语句；简洁注释；命名驼峰
  - 日志：简洁拟人，无特殊符号
  - 非必要不随意创建无关文件，不删除文件（除非先获得确认）

## 构建与验证
- VS/MSBuild（Release x64）：
## 开发与协作说明
- 平台：Windows/MSBuild；本仓库在mac端编辑，请在Windows上以 Release x64 构建和运行。
- 构建：
  "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\MSBuild.exe" SphereSeg3D.sln -t:Build -noLogo -property:Configuration=Release -property:Platform=x64 -maxcpucount -verbosity:minimal
- 代码规范：UTF-8 + CRLF，Allman 风格；英文日志；函数/类命名遵循约定；不改 Detector.cpp/h。
- 同步规则：Python 改动需同步到 C++，接口对齐，日志一致。
- 运行（建议在 Release 目录下执行，依赖和模型已就位）:
  - 切到 SphereSeg3D\\x64\\Release
  - .\\SphereSeg3DApp.exe D:\\MyWorkSpace\\Code\\GitProjects\\WeltDetect\\data\\processed\\Slice_t .\\Config\\bestV5.onnx
  - 输出目录为当前工作目录下的 .\\output

- C++工程构建后，生成的dll和exe都放在SphereSeg3D\x64\Release目录下。
- 后续工作建议：
  1) 裁剪保存聚焦层 PNG 与 RAW 的更多参数化；


### 输入支持
- 路径包含 ImageParam.ini：按 INI 加载 RAW 切片（NameTemplate、Begin/EndIndex、dtype 等）。
- 无 INI（png或dcm文件）：按文件名后缀数字大小排序加载（不能用字典序），和python侧保持一致。

### 保存阶段（完全复刻）
- 合并所有目标 mask → 逐切片 2D 空洞填充 → 3D 26 邻域连通标记。
- 对每个连通组件取原体素最大值 comp_max[label]。
- 二值膨胀一轮（26 邻域），对标签做一次灰度膨胀（标签局部最大值传播）。
- 在膨胀区域内做值校正：out = (1 - s) * val + s * comp_max[label]（s=0.5）。
- 背景随机采样原体素 ×0.7 填充；空洞保持 0。
- 输出 processed_allSlice_{W}x{H}x{Z}.raw 与 focus_layer_segmentation.png。

  3) 增加单元测试（对齐样例数据，核对mask体素和统计）。

  - 构建解决方案：
    "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\MSBuild.exe" SphereSeg3D.sln -t:Build -noLogo -property:Configuration=Release -property:Platform=x64 -maxcpucount -verbosity:minimal

## 开发规范（关键）
- 与 Python 同步：
  - Python 新增/修改函数 → 在 C++ 中创建/更新相同签名的对应函数，并更新调用链
  - 调试输出/日志信息保持一致，文件名/列顺序一致
- 代码与工程：
  - 不改 Detector.cpp/h；其余模块尽量解耦与轻依赖
  - 仅必要时新增文件，保持工程整洁
- 环境：
  - Python 侧研究/验证使用 conda activate weltdetect-ct-env(mac下)，或者sphereSeg(win)；

## 开发的标准流程
- 1. 分析新任务新需求是否有问题，若有则当场提出；
- 2. 若没有歧义则开始按要求完成任务；
- 3. 然后对更改的代码运行编译指令并debug直到无编译问题；
- 4. 对同一个测试数据：D:\MyWorkSpace\Code\GitProjects\WeltDetect\data\processed\Slice_t，分别用刚编译生成的SphereSeg3D\x64\Release\SphereSeg3DApp.exe和python侧的main.py处理，对照检查结果是否一致（python的运行环境需要切换到sphereSeg下，conda activate sphereSeg）；
- 5. 若结果一致则结束任务，否则重复2-5直到结果一致。

## 目录参考

## 常见问题


## Git 工作流建议



