# SphereSeg3D - 3D球体分割C++库

本项目实现了基于 **OpenCV 4.x** 和 **ONNX Runtime** 的3D球体自动分割，支持：
- 3D体数据的智能球体检测和分割
- AI驱动的自动聚焦和目标识别
- 高性能处理
- 自适应分割算法

## 📦 交付文件结构

```
交付文件汇总-8.21/
├── README.md                    # 本使用说明文档
├── include/                     # 头文件目录
│   └── SphereSeg3D_Public.h     # 公共API头文件
├── x64/Release/                 # 运行时文件
│   ├── SphereSeg3D.dll          # 核心处理库
│   ├── SphereSeg3D.lib          # 导入库文件
│   ├── SphereSeg3DApp.exe       # 示例应用程序
│   └── Config/
│       └── bestV5.onnx          # AI模型文件
└── SphereSeg3DApp/              # 示例程序源码
    ├── main.cpp                 # 完整使用示例
    └── SphereSeg3DApp.vcxproj   # VS项目文件

```

## 🚀 快速开始

### 1. 运行示例程序
```bash
cd x64/Release
SphereSeg3DApp.exe [输入数据路径]
```

### 2. 集成到你的项目
1. 复制 `SphereSeg3D.dll` 到你的程序目录
2. 包含 `include/SphereSeg3D_Public.h` 头文件
3. 链接 `SphereSeg3D.lib` 库文件
4. 确保 `Config/bestV5.onnx` 模型文件可访问

### 3. 依赖库要求
**注意：需要自行安装以下依赖库**
- `opencv_world4120.dll` - OpenCV 4.12.0
- `onnxruntime.dll` - ONNX Runtime 1.x

## 环境配置

### 1. 系统依赖
- C++17 或更高版本
- Windows 10/11 x64
- Visual Studio 2019/2022 Runtime
- OpenCV 4.12.0
- ONNX Runtime 1.x

### 2. 安装 OpenCV 4.x
下载 OpenCV release库并安装：

```bash
https://github.com/opencv/opencv/releases/download/4.12.0/opencv-4.12.0-windows.exe
```

### 3. 安装 ONNX Runtime
下载对应的 onnxruntime 压缩包并解压：

```bash
https://github.com/microsoft/onnxruntime/releases/download/v1.22.1/onnxruntime-win-x64-1.22.1.zip
```

### 4. 部署配置
将以下文件复制到应用程序目录：

```bash
SphereSeg3D.dll             # 核心处理库
opencv_world4120.dll        # OpenCV依赖
onnxruntime.dll             # ONNX运行时
Config/bestV5.onnx          # AI模型文件
```

## 使用方法

### 1. 准备模型和数据

将 bestV5.onnx 模型放到dll同路径下的 Config/ 目录中：
- 输入数据：3D体数据（RAW+ini文件格式或图像序列）

### 2. 基本调用示例

```cpp
#include "SphereSeg3D_Public.h"
#include <iostream>

int main() {
    try {
        // 初始化处理器
        SphereSeg3DProcessor processor;

        // 配置处理参数
        ProcessingOptions options;
        options.modelPath = "Config/sphere_detection.onnx";
        options.useCuda = false;
        options.numThreads = 1;

        // 处理体数据
        ProcessedVolume result;
        bool success = processor.processVolumeFromMemory(
            volumeData,     // 原始体数据指针
            width,          // 体数据宽度
            height,         // 体数据高度
            depth,          // 体数据深度
            isSigned,       // 数据类型 (true=int16, false=uint16)
            options,        // 处理配置
            result          // 输出结果
        );

        if (success) {
            std::cout << "处理成功！输出尺寸: "
                      << result.width << "x" << result.height << "x" << result.depth << std::endl;

            // 使用处理结果...

            // 释放内存
            processor.freeProcessedVolume(result);
        }
    }
    catch (const std::exception& ex) {
        std::cerr << "处理错误: " << ex.what() << std::endl;
        return -1;
    }
    return 0;
}
```

##  API 说明

### SphereSeg3DProcessor 类

| 方法 | 说明 |
|------|------|
| `processVolumeFromMemory(...)` | 从内存处理体数据，返回分割结果 |
| `processVolumeFromDirectory(...)` | 从图像文件目录处理体数据 |
| `freeProcessedVolume(volume)` | 释放处理结果的内存 |
| `getLastError()` | 获取最后的错误信息 |

---

### ProcessingOptions 结构体

```cpp
struct ProcessingOptions {
    const char* modelPath;    // ONNX模型文件路径
    bool useCuda;            // 启用GPU加速 (默认: false)
    int numThreads;          // 处理线程数 (默认: 1)
};
```

### ProcessedVolume 结构体

```cpp
struct ProcessedVolume {
    void* data;              // 处理后的体数据指针
    int width, height, depth; // 体数据尺寸
    bool isSigned;           // 数据类型标识
    int cropX0, cropY0;      // 裁剪偏移信息
};
```


## 集成注意事项

1. **模型文件**：确保ONNX模型文件路径正确且可访问
2. **依赖库**：所有DLL文件必须在同一目录或系统PATH中
3. **数据格式**：调用前自己转换为输入的格式即可
4. **内存管理**：使用后必须调用freeProcessedVolume释放内存
5. **线程安全**：建议每个线程使用独立的处理器实例

---

*最后更新：2025年8月21日22:36