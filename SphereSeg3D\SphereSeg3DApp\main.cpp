// Windows(CR LF), UTF-8
#ifdef _WIN32
#include <windows.h>
#endif

#include "../SphereSeg3DCpp/SphereSeg3D_Public.h"
#include <iostream>
#include <filesystem>
#include <fstream>
#include <vector>
#include <chrono>
#include <iomanip>

namespace fs = std::filesystem;

// Save processed volume to files (moved from DLL to APP)
bool saveProcessedVolume(const ProcessedVolume& volume,
                        const char* outputPath,
                        const char* baseFileName = nullptr)
{
    if (!volume.data || !outputPath)
    {
        std::cerr << "Invalid parameters for saving" << std::endl;
        return false;
    }

    try
    {
        // Implementation for saving processed volume
        std::string baseName = baseFileName ? baseFileName : "processed_volume";

        // Save RAW file
        std::string rawFileName = baseName + "_" +
                                 std::to_string(volume.width) + "x" +
                                 std::to_string(volume.height) + "x" +
                                 std::to_string(volume.depth) + ".raw";

        std::filesystem::path rawPath = std::filesystem::path(outputPath) / rawFileName;

        std::ofstream rawFile(rawPath, std::ios::binary);
        if (!rawFile)
        {
            std::cerr << "Failed to create output file: " << rawPath << std::endl;
            return false;
        }

        size_t totalBytes = static_cast<size_t>(volume.width) * volume.height * volume.depth;
        totalBytes *= volume.isSigned ? sizeof(int16_t) : sizeof(uint16_t);

        rawFile.write(static_cast<const char*>(volume.data), totalBytes);
        rawFile.close();

        // Save INI file
        std::filesystem::path iniPath = rawPath;
        iniPath.replace_extension(".ini");
        std::ofstream iniFile(iniPath);
        if (iniFile)
        {
            iniFile << "[RawImageInfo]\n";
            iniFile << "Width=" << volume.width << "\n";
            iniFile << "Height=" << volume.height << "\n";
            iniFile << "BitsAllocated=16\n";
            iniFile << "PixelRepresentation=" << (volume.isSigned ? 1 : 0) << "\n";
            iniFile << "[FileModule]\n";
            iniFile << "BeginIndex=0\n";
            iniFile << "EndIndex=" << (volume.depth - 1) << "\n";
            iniFile << "NameTemplate=Slice_%d.raw\n";
            iniFile.close();
        }

        return true;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Save exception: " << e.what() << std::endl;
        return false;
    }
    catch (...)
    {
        std::cerr << "Unknown save exception" << std::endl;
        return false;
    }
}

static std::string getDefaultModelPath(const char* modelName)
{
#ifdef _WIN32
    char exePathBuffer[MAX_PATH] = {0};
    DWORD pathLength = GetModuleFileNameA(nullptr, exePathBuffer, MAX_PATH);
    fs::path exePath = (pathLength > 0) ? fs::path(exePathBuffer) : fs::current_path();
#else
    fs::path exePath = fs::absolute("./");
#endif
    fs::path exeDir = exePath.parent_path();
    fs::path modelPath = exeDir / "Config" / modelName;
    return modelPath.string();
}

void printUsage(const char* programName)
{
    std::cout << "SphereSeg3D Demo Application\n";
    std::cout << "Usage: " << programName << " <input_directory> [model_path]\n";
    std::cout << "\nArguments:\n";
    std::cout << "  input_directory  Directory containing volume data (with ImageParam.ini)\n";
    std::cout << "  model_path       Optional path to ONNX model (default: ./Config/bestV5.onnx)\n";
    std::cout << "\nExample:\n";
    std::cout << "  " << programName << " ./data/processed/Slice_t\n";
    std::cout << "  " << programName << " ./data/processed/Slice_t ./models/custom.onnx\n";
}



// Load volume data from directory into memory
bool loadVolumeFromDirectory(const std::string& inputPath,
                            std::vector<uint16_t>& volumeData,
                            int& width, int& height, int& depth, bool& isSigned)
{
    fs::path iniPath = fs::path(inputPath) / "ImageParam.ini";
    if (!fs::exists(iniPath))
    {
        std::cerr << "ImageParam.ini not found in " << inputPath << std::endl;
        return false;
    }

    // Parse INI file
    std::ifstream ifs(iniPath);
    std::string line;
    int bitsAllocated = 16, pixelRepresentation = 0;
    int beginIndex = 0, endIndex = -1;
    std::string nameTemplate = "slice%d.bin";

    while (std::getline(ifs, line))
    {
        auto pos = line.find('=');
        if (pos == std::string::npos) continue;

        std::string key = line.substr(0, pos);
        std::string value = line.substr(pos + 1);

        if (key.find("Width") != std::string::npos) width = std::stoi(value);
        else if (key.find("Height") != std::string::npos) height = std::stoi(value);
        else if (key.find("BitsAllocated") != std::string::npos) bitsAllocated = std::stoi(value);
        else if (key.find("PixelRepresentation") != std::string::npos) pixelRepresentation = std::stoi(value);
        else if (key.find("BeginIndex") != std::string::npos) beginIndex = std::stoi(value);
        else if (key.find("EndIndex") != std::string::npos) endIndex = std::stoi(value);
        else if (key.find("NameTemplate") != std::string::npos) nameTemplate = value;
    }

    if (width <= 0 || height <= 0 || endIndex < beginIndex)
    {
        std::cerr << "Invalid ImageParam.ini parameters" << std::endl;
        return false;
    }

    depth = endIndex - beginIndex + 1;
    isSigned = (bitsAllocated == 16 && pixelRepresentation == 1);

    // Load volume data
    size_t totalVoxels = static_cast<size_t>(width) * height * depth;
    volumeData.resize(totalVoxels);

    size_t sliceVoxels = static_cast<size_t>(width) * height;
    size_t sliceBytes = sliceVoxels * sizeof(uint16_t);

    for (int z = beginIndex; z <= endIndex; ++z)
    {
        char fileName[256];
        std::snprintf(fileName, sizeof(fileName), nameTemplate.c_str(), z);
        fs::path slicePath = fs::path(inputPath) / fileName;

        std::ifstream sliceFile(slicePath, std::ios::binary);
        if (!sliceFile)
        {
            std::cerr << "Failed to open slice file: " << slicePath << std::endl;
            return false;
        }

        size_t offset = static_cast<size_t>(z - beginIndex) * sliceVoxels;
        sliceFile.read(reinterpret_cast<char*>(volumeData.data() + offset), sliceBytes);

        if (!sliceFile.good())
        {
            std::cerr << "Failed to read slice file: " << slicePath << std::endl;
            return false;
        }
    }

    std::cout << "Volume loaded: " << width << "x" << height << "x" << depth
              << " (" << (isSigned ? "int16" : "uint16") << ")" << std::endl;

    return true;
}

int main(int argc, char* argv[])
{
    std::cout << "SphereSeg3D Memory Processing Demo\n";
    std::cout << "Demonstrates processVolumeFromMemory interface\n\n";

    if (argc < 2)
    {
        printUsage(argv[0]);
        return 1;
    }

    try
    {
        // Parse command line arguments
        fs::path inputDir = argv[1];
        std::string modelPath = (argc >= 3) ? argv[2] : getDefaultModelPath("bestV5.onnx");
        fs::path outputDir = inputDir / "output";

        std::cout << "Input Directory: " << inputDir << "\n";
        std::cout << "Model Path: " << modelPath << "\n";
        std::cout << "Output Directory: " << outputDir << "\n\n";

        // Verify input directory and model file exist
        if (!fs::exists(inputDir) || !fs::is_directory(inputDir))
        {
            std::cerr << "Error: Input directory does not exist: " << inputDir << "\n";
            return 1;
        }
        if (!fs::exists(modelPath))
        {
            std::cerr << "Error: Model file does not exist: " << modelPath << "\n";
            return 1;
        }

        // Step 1: Load volume data into memory
        std::cout << "=== Step 1: Loading Volume Data ===\n";
        std::vector<uint16_t> volumeData;
        int width, height, depth;
        bool isSigned;

        auto loadStart = std::chrono::high_resolution_clock::now();
        if (!loadVolumeFromDirectory(inputDir.string(), volumeData, width, height, depth, isSigned))
        {
            std::cerr << "Failed to load volume data from directory\n";
            return 1;
        }
        auto loadEnd = std::chrono::high_resolution_clock::now();
        auto loadTime = std::chrono::duration<double>(loadEnd - loadStart).count();
        std::cout << "Data loading completed in " << std::fixed << std::setprecision(3) << loadTime << " s\n\n";

        // Step 2: Initialize processor and configure options
        std::cout << "=== Step 2: Initialize Processor ===\n";
        SphereSeg3DProcessor processor;

        ProcessingOptions options;
        options.modelPath = modelPath.c_str();
        options.useCuda = false;
        options.numThreads = 1;

        std::cout << "Processor initialized with options:\n";
        std::cout << "  Model: " << options.modelPath << "\n";
        std::cout << "  CUDA: " << (options.useCuda ? "Enabled" : "Disabled") << "\n";
        std::cout << "  Threads: " << options.numThreads << "\n\n";

        // Step 3: Process volume from memory
        std::cout << "=== Step 3: Process Volume from Memory ===\n";
        ProcessedVolume result;

        auto processStart = std::chrono::high_resolution_clock::now();
        bool success = processor.processVolumeFromMemory(volumeData.data(),
                                                        width, height, depth,
                                                        isSigned,
                                                        options,
                                                        result);
        auto processEnd = std::chrono::high_resolution_clock::now();
        auto processTime = std::chrono::duration<double>(processEnd - processStart).count();

        if (success)
        {
            std::cout << "Processing completed successfully!\n";
            std::cout << "Result dimensions: " << result.width << "x" << result.height << "x" << result.depth << "\n";
            std::cout << "Processing time: " << std::fixed << std::setprecision(3) << processTime << " s\n\n";

            // Step 4: Save results
            std::cout << "=== Step 4: Save Results ===\n";
            fs::create_directories(outputDir);

            if (saveProcessedVolume(result, outputDir.string().c_str(), "processed_volume"))
            {
                // Display specific file paths
                std::string rawFile = (outputDir / ("processed_allSlice_" + std::to_string(result.width) + "x" +
                                                   std::to_string(result.height) + "x" + std::to_string(result.depth) + ".raw")).string();
                std::string iniFile = (outputDir / ("processed_allSlice_" + std::to_string(result.width) + "x" +
                                                   std::to_string(result.height) + "x" + std::to_string(result.depth) + ".ini")).string();

                std::cout << "\nOutput files saved:" << std::endl;
                std::cout << "  3D Volume: " << rawFile << std::endl;
                std::cout << "  Config File: " << iniFile << std::endl;
                std::cout << "  ImageJ import: width=" << result.width << ", height=" << result.height
                          << ", slices=" << result.depth << ", dtype=" << (result.isSigned ? "int16" : "uint16") << "\n\n";
            }
            else
            {
                std::cerr << "Warning: Failed to save results\n\n";
            }

            // Timing information removed - DLL focuses on processing only

            // Clean up
            processor.freeProcessedVolume(result);

            std::cout << "\nProcessing completed successfully.\n";
        }
        else
        {
            std::cerr << "\nError: Volume processing failed!\n";
            const char* error = processor.getLastError();
            if (error && strlen(error) > 0)
            {
                std::cerr << "Error details: " << error << "\n";
            }
            return 1;
        }
    }
    catch (const std::exception& e)
    {
        std::cerr << "Exception occurred: " << e.what() << "\n";
        return 1;
    }
    catch (...)
    {
        std::cerr << "Unknown exception occurred!\n";
        return 1;
    }

    return 0;
}
