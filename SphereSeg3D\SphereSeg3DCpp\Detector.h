#pragma once
#include <onnxruntime_cxx_api.h>
#include <opencv2/opencv.hpp>
#include <opencv2/dnn.hpp>
#include <string>
#include <vector>

struct Detection {
    cv::Rect box;      // cx  cy  w  h
    int classId;
    float confidence;
    int priority = -1; // 类别优先级
    int z = 0;
};

class YoloOnnxDetector {
public:
    YoloOnnxDetector(const std::string& modelPath, bool useCuda = false, int numThreads = 1, int width = 1024, int height = 1024);
    void LoadLabels(const std::vector<std::string>& labels);
    std::vector<Detection> Detect(const cv::Mat& image);
    std::vector<std::vector<Detection>> DetectBatch(const std::vector<cv::Mat>& images);
    void DrawDetections(cv::Mat& image, const std::vector<Detection>& detections);
    
private:
    cv::Mat Preprocess(const cv::Mat& image, float& xFactor, float& yFactor);

    std::vector<Detection> Postprocess(
        const cv::Mat& detOutput, 
        float xFactor, 
        float yFactor
    );

    std::vector<Detection> PostprocessWithPriority(
        const cv::Mat& detOutput,
        float xFactor,
        float yFactor,
        float iouThreshold = 0.5,
        const std::vector<int>& classPriority = { 1, 2, 0, 3} // 例如 {1, 2, 0, 3}
    );

private:
    Ort::Env env_;
    Ort::SessionOptions sessionOptions_;
    Ort::Session session_{nullptr};

    int inputB_{1}, inputC_{0}, inputH_{0}, inputW_{0};
    int numPredictions_{0}, numAttributes_{0};

    float confThreshold_{0.3f};
    float nmsThreshold_{0.35f};
    bool inputDynamic_ = false;


    std::vector<int64_t> modelInputShape_;
    std::vector<std::string> inputNodeNames_;
    std::vector<std::string> outputNodeNames_;
    std::vector<std::string> labels_;
};
