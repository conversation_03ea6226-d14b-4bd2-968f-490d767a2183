#include "FocusAnalyzer.h"
#include <iostream>
#include <opencv2/opencv.hpp>

using namespace std;

namespace ss3d {

std::vector<int> FocusAnalyzer::AMPD(const std::vector<double>& data)
{
    // Keep identical AMPD logic as original to ensure same behavior
    int n = static_cast<int>(data.size());
    if (n < 3)
    {
        return {};
    }
    int max_k = n / 2;
    std::vector<int> arr_rowsum(max_k, 0);
    for (int k = 1; k <= max_k; ++k)
    {
        int count_peak = 0;
        for (int i = k; i < n - k; ++i)
        {
            if (data[i] > data[i - k] && data[i] > data[i + k])
            {
                count_peak++;
            }
        }
        arr_rowsum[k - 1] = -count_peak;
    }
    int min_index = static_cast<int>(std::min_element(arr_rowsum.begin(), arr_rowsum.end()) - arr_rowsum.begin());
    int max_window_length = min_index + 1;
    std::vector<bool> candidate_mask(n, true);
    std::vector<int> p_data(n, 0);
    for (int k = 1; k <= max_window_length; ++k)
    {
        for (int i = k; i < n - k; ++i)
        {
            if (candidate_mask[i] && data[i] > data[i - k] && data[i] > data[i + k])
            {
                p_data[i]++;
            }
        }
        for (int i = 0; i <= k; ++i) candidate_mask[i] = false;
        for (int i = n - k - 1; i < n; ++i) candidate_mask[i] = false;
    }
    std::vector<int> peaks;
    for (int i = 0; i < n; ++i)
    {
        if (p_data[i] == max_window_length)
        {
            peaks.push_back(i);
        }
    }
    return peaks;
}

std::vector<double> FocusAnalyzer::tenengradFocusScores(const VolumeData& volume)
{
    const int W = volume.width;
    const int H = volume.height;
    const int Z = volume.depth;
    std::vector<double> scores(Z, 0.0);
    auto get_slice = [&](int z) -> cv::Mat
    {
        size_t slice_voxels = static_cast<size_t>(W) * H;
        size_t offset = static_cast<size_t>(z) * slice_voxels;
        if (volume.is_signed)
        {
            const int16_t* ptr = reinterpret_cast<const int16_t*>(volume.data.get()) + offset;
            return cv::Mat(H, W, CV_16SC1, const_cast<int16_t*>(ptr));
        }
        else
        {
            const uint16_t* ptr = reinterpret_cast<const uint16_t*>(volume.data.get()) + offset;
            return cv::Mat(H, W, CV_16UC1, const_cast<uint16_t*>(ptr));
        }
    };
    auto tenengrad = [&](const cv::Mat& img16) -> double
    {
        double minv = 0.0, maxv = 0.0;
        cv::minMaxLoc(img16, &minv, &maxv);
        double scale = (maxv > minv) ? 255.0 / (maxv - minv) : 1.0;
        cv::Mat norm8;
        img16.convertTo(norm8, CV_8U, scale, -minv * scale);
        cv::Mat gx, gy;
        cv::Sobel(norm8, gx, CV_64F, 1, 0, 3);
        cv::Sobel(norm8, gy, CV_64F, 0, 1, 3);
        cv::Mat fm = gx.mul(gx) + gy.mul(gy);
        return cv::mean(fm)[0];
    };
    for (int z = 0; z < Z; ++z)
    {
        scores[z] = tenengrad(get_slice(z));
    }
    return scores;
}

} // namespace ss3d

