#include "Segmenter.h"
#include <queue>
#include <iostream>
#include <filesystem>
#include <random>
#include <tuple>

using namespace std;

namespace ss3d {

// 二值图的孔洞填充：从边界开始做泛洪，将未标记的背景视为孔洞并填为前景
std::vector<bool> Segmenter::binaryFillHoles2D(const std::vector<bool>& slice, int width, int height)
{
    std::vector<bool> filled(slice);

    std::vector<bool> background(width * height, false);
    std::queue<std::pair<int, int>> q;
    auto idx = [&](int y, int x) -> size_t { return static_cast<size_t>(y) * width + x; };
    auto inBounds = [&](int y, int x) -> bool { return y >= 0 && y < height && x >= 0 && x < width; };

    for (int y = 0; y < height; ++y)
    {
        for (int x = 0; x < width; ++x)
        {
            if ((y == 0 || y == height - 1 || x == 0 || x == width - 1) && !slice[idx(y, x)])
            {
                if (!background[idx(y, x)])
                {
                    background[idx(y, x)] = true;
                    q.push({y, x});
                }
            }
        }
    }

    int dy[] = {-1, 1, 0, 0};
    int dx[] = {0, 0, -1, 1};

    while (!q.empty())
    {
        auto [y, x] = q.front();
        q.pop();
        for (int i = 0; i < 4; ++i)
        {
            int ny = y + dy[i];
            int nx = x + dx[i];
            if (inBounds(ny, nx) && !slice[idx(ny, nx)] && !background[idx(ny, nx)])
            {
                background[idx(ny, nx)] = true;
                q.push({ny, nx});
            }
        }
    }

    for (int y = 0; y < height; ++y)
    {
        for (int x = 0; x < width; ++x)
        {
            if (!background[idx(y, x)])
            {
                filled[idx(y, x)] = true;
            }
        }
    }

    return filled;
}

// 生成三维分割结果：合并掩码、连通域、形态学扩展、二维孔洞修复、值域校正与结果组装
bool Segmenter::generateSegmentationResult(const VolumeData& volume,
                                           const std::vector<std::vector<bool>>& sphereMasks,
                                           int targetLayer,
                                           const std::string& outputPath,
                                           SegmentationResult& result)
{
    if (sphereMasks.empty())
    {
        std::cout << "没有有效的球体掩码" << std::endl;
        return false;
    }

    namespace fs = std::filesystem;
    if (!outputPath.empty())
    {
        fs::create_directories(outputPath);
    }

    int Z = volume.depth, H = volume.height, W = volume.width;
    size_t voxels = static_cast<size_t>(W) * H * Z;

    std::cout << "开始生成三维分割结果..." << std::endl;
    std::cout << "体素维度: " << Z << " x " << H << " x " << W << std::endl;
    std::cout << "数据类型: " << (volume.is_signed ? "int16" : "uint16") << std::endl;
    std::cout << "球体数量: " << sphereMasks.size() << std::endl;

    // 合并所有球体掩码
    std::vector<bool> combined_mask(voxels, false);
    for (const auto& mask : sphereMasks)
    {
        for (size_t i = 0; i < voxels && i < mask.size(); ++i)
        {
            if (mask[i])
            {
                combined_mask[i] = true;
            }
        }
    }

    // 三维连通域标记（26邻域）
    std::vector<int> labeled_mask(voxels, 0);
    int num_features = 0;
    auto idx3 = [&](int z, int y, int x) -> size_t { return static_cast<size_t>(z) * W * H + static_cast<size_t>(y) * W + x; };
    auto inBounds3 = [&](int z, int y, int x) -> bool { return z >= 0 && z < Z && y >= 0 && y < H && x >= 0 && x < W; };

    std::vector<std::tuple<int, int, int>> offsets;
    for (int dz = -1; dz <= 1; ++dz)
        for (int dy = -1; dy <= 1; ++dy)
            for (int dx = -1; dx <= 1; ++dx)
                if (dz != 0 || dy != 0 || dx != 0)
                    offsets.push_back({dz, dy, dx});

    for (int z = 0; z < Z; ++z)
    {
        for (int y = 0; y < H; ++y)
        {
            for (int x = 0; x < W; ++x)
            {
                size_t i = idx3(z, y, x);
                if (combined_mask[i] && labeled_mask[i] == 0)
                {
                    ++num_features;
                    std::queue<std::tuple<int, int, int>> q;
                    q.push({z, y, x});
                    labeled_mask[i] = num_features;

                    while (!q.empty())
                    {
                        auto [cz, cy, cx] = q.front();
                        q.pop();
                        for (auto [dz, dy, dx] : offsets)
                        {
                            int nz = cz + dz, ny = cy + dy, nx = cx + dx;
                            if (inBounds3(nz, ny, nx))
                            {
                                size_t ni = idx3(nz, ny, nx);
                                if (combined_mask[ni] && labeled_mask[ni] == 0)
                                {
                                    labeled_mask[ni] = num_features;
                                    q.push({nz, ny, nx});
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 计算每个连通域的最大值，用于后续值域校正
    std::vector<double> comp_max(num_features + 1, 0.0);
    if (num_features > 0)
    {
        if (volume.is_signed)
        {
            const int16_t* src = reinterpret_cast<const int16_t*>(volume.data.get());
            for (size_t i = 0; i < voxels; ++i)
            {
                if (labeled_mask[i] > 0)
                {
                    int label = labeled_mask[i];
                    comp_max[label] = std::max(comp_max[label], static_cast<double>(src[i]));
                }
            }
        }
        else
        {
            const uint16_t* src = reinterpret_cast<const uint16_t*>(volume.data.get());
            for (size_t i = 0; i < voxels; ++i)
            {
                if (labeled_mask[i] > 0)
                {
                    int label = labeled_mask[i];
                    comp_max[label] = std::max(comp_max[label], static_cast<double>(src[i]));
                }
            }
        }
    }

    // 形态学膨胀（单次，26邻域），并同步扩展标签
    std::vector<bool> dilated_mask = combined_mask;
    std::vector<int> dilated_labels = labeled_mask;
    for (int z = 0; z < Z; ++z)
    {
        for (int y = 0; y < H; ++y)
        {
            for (int x = 0; x < W; ++x)
            {
                size_t i = idx3(z, y, x);
                if (combined_mask[i])
                {
                    for (auto [dz, dy, dx] : offsets)
                    {
                        int nz = z + dz, ny = y + dy, nx = x + dx;
                        if (inBounds3(nz, ny, nx))
                        {
                            size_t ni = idx3(nz, ny, nx);
                            dilated_mask[ni] = true;
                            if (dilated_labels[ni] == 0)
                            {
                                dilated_labels[ni] = labeled_mask[i];
                            }
                        }
                    }
                }
            }
        }
    }

    // 逐层二维孔洞修复，得到孔洞掩码
    std::vector<bool> hole_mask(voxels, false);
    for (int z = 0; z < Z; ++z)
    {
        std::vector<bool> slice2d(W * H);
        for (int y = 0; y < H; ++y)
        {
            for (int x = 0; x < W; ++x)
            {
                size_t i3 = static_cast<size_t>(z) * W * H + static_cast<size_t>(y) * W + x;
                size_t i2 = static_cast<size_t>(y) * W + x;
                slice2d[i2] = combined_mask[i3];
            }
        }
        bool hasAny = false;
        for (bool v : slice2d) { if (v) { hasAny = true; break; } }
        if (hasAny)
        {
            std::vector<bool> filled = binaryFillHoles2D(slice2d, W, H);
            for (int y = 0; y < H; ++y)
            {
                for (int x = 0; x < W; ++x)
                {
                    size_t i3 = static_cast<size_t>(z) * W * H + static_cast<size_t>(y) * W + x;
                    size_t i2 = static_cast<size_t>(y) * W + x;
                    if (filled[i2] && !slice2d[i2])
                    {
                        hole_mask[i3] = true;
                    }
                }
            }
        }
    }

    // 值域校正并组装输出体数据
    const double max_value_scale = 0.5;
    size_t bytesPerVoxel = volume.is_signed ? sizeof(int16_t) : sizeof(uint16_t);
    result.width = W; result.height = H; result.depth = Z; result.is_signed = volume.is_signed; result.crop_x0 = 0; result.crop_y0 = 0;
    result.data = std::make_unique<uint8_t[]>(voxels * bytesPerVoxel);

    std::vector<double> correct_volume(voxels, 0.0);
    std::vector<double> segmented_volume(voxels, 0.0);

    if (volume.is_signed)
    {
        const int16_t* src = reinterpret_cast<const int16_t*>(volume.data.get());
        for (size_t i = 0; i < voxels; ++i)
        {
            if (dilated_mask[i] && num_features > 0)
            {
                int label = dilated_labels[i];
                if (label > 0 && label <= num_features)
                {
                    double orig = static_cast<double>(src[i]);
                    double mx = comp_max[label];
                    correct_volume[i] = (1.0 - max_value_scale) * orig + max_value_scale * mx;
                }
            }
        }
    }
    else
    {
        const uint16_t* src = reinterpret_cast<const uint16_t*>(volume.data.get());
        for (size_t i = 0; i < voxels; ++i)
        {
            if (dilated_mask[i] && num_features > 0)
            {
                int label = dilated_labels[i];
                if (label > 0 && label <= num_features)
                {
                    double orig = static_cast<double>(src[i]);
                    double mx = comp_max[label];
                    correct_volume[i] = (1.0 - max_value_scale) * orig + max_value_scale * mx;
                }
            }
        }
    }

    if (volume.is_signed)
    {
        const int16_t* src = reinterpret_cast<const int16_t*>(volume.data.get());
        int16_t* dst = reinterpret_cast<int16_t*>(result.data.get());

        for (size_t i = 0; i < voxels; ++i)
        {
            if (dilated_mask[i])
            {
                segmented_volume[i] = correct_volume[i];
            }
        }

        std::vector<int16_t> bg_values;
        bg_values.reserve(voxels);
        for (size_t i = 0; i < voxels; ++i)
        {
            if (!dilated_mask[i])
            {
                bg_values.push_back(src[i]);
            }
        }

        if (!bg_values.empty())
        {
            std::random_device rd;
            std::mt19937 rng(rd());
            std::uniform_int_distribution<size_t> dist(0, bg_values.size() - 1);
            for (size_t i = 0; i < voxels; ++i)
            {
                if (!dilated_mask[i])
                {
                    segmented_volume[i] = static_cast<double>(bg_values[dist(rng)]);
                }
            }
        }

        for (size_t i = 0; i < voxels; ++i)
        {
            dst[i] = static_cast<int16_t>(std::round(segmented_volume[i]));
        }
    }
    else
    {
        const uint16_t* src = reinterpret_cast<const uint16_t*>(volume.data.get());
        uint16_t* dst = reinterpret_cast<uint16_t*>(result.data.get());

        for (size_t i = 0; i < voxels; ++i)
        {
            if (dilated_mask[i])
            {
                segmented_volume[i] = correct_volume[i];
            }
        }

        std::vector<uint16_t> bg_values;
        bg_values.reserve(voxels);
        for (size_t i = 0; i < voxels; ++i)
        {
            if (!dilated_mask[i])
            {
                bg_values.push_back(src[i]);
            }
        }

        if (!bg_values.empty())
        {
            std::random_device rd;
            std::mt19937 rng(rd());
            std::uniform_int_distribution<size_t> dist(0, bg_values.size() - 1);
            for (size_t i = 0; i < voxels; ++i)
            {
                if (!dilated_mask[i])
                {
                    segmented_volume[i] = static_cast<double>(bg_values[dist(rng)]);
                }
            }
        }

        for (size_t i = 0; i < voxels; ++i)
        {
            dst[i] = static_cast<uint16_t>(std::round(segmented_volume[i]));
        }
    }

    (void)targetLayer; // 目前生成结果不依赖 targetLayer，仅保留接口一致性
    return true;
}

} // namespace ss3d

