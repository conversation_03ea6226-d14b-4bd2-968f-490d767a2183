#pragma once

#include <vector>
#include <string>
#include "SphereSeg3D.h"

namespace ss3d {

class Segmenter
{
public:
    /// Generate 3D segmentation result, same effect as original implementation.
    bool generateSegmentationResult(const VolumeData& volume,
                                    const std::vector<std::vector<bool>>& sphereMasks,
                                    int targetLayer,
                                    const std::string& outputPath,
                                    SegmentationResult& result);

    /// 2D binary hole filling for a slice.
    std::vector<bool> binaryFillHoles2D(const std::vector<bool>& slice,
                                        int width,
                                        int height);
};

} // namespace ss3d

