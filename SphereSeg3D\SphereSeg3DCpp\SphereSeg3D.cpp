#include "SphereSeg3D.h"
#include "Detector.h"
#include <iostream>
#include <iomanip>
#include <chrono>
#include <filesystem>
#include <fstream>
#include <algorithm>
#include <cmath>
#include <random>
#include <deque>
#include <limits>
#include <cstring>
#define NOMINMAX  // Prevent Windows.h from defining min/max macros
#include <windows.h>
#include <io.h>
#include <fcntl.h>

#include <memory>
#include <map>
#include <optional>
#include <numeric>
#include <queue>
#include <opencv2/opencv.hpp>
#include "FocusAnalyzer.h"
#include "Segmenter.h"

// Implementation class
class SphereSeg3D::Impl
{
public:
    std::unique_ptr<YoloOnnxDetector> detector;
    bool useCuda = false;
    int numThreads = 1;

    Impl() = default;
};

// Initialize sphere segmentation processor
SphereSeg3D::SphereSeg3D() : pImpl(new Impl())
{
    // Configure console encoding for proper text display
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);
}
SphereSeg3D::~SphereSeg3D() { delete pImpl; }
// Process 3D volume data from memory buffer
// Parameters:
//   volumeData: Raw volume data buffer
//   width, height, depth: Volume dimensions
//   isSigned: Data type (int16 if true, uint16 if false)
//   config: Processing configuration
//   result: Output segmentation result
bool SphereSeg3D::processVolumeFromMemory(const void* volumeData,
                                         int width,
                                         int height,
                                         int depth,
                                         bool isSigned,
                                         const ProcessingConfig& config,
                                         SegmentationResult& result)
{
    using clock = std::chrono::high_resolution_clock;
    auto totalStart = clock::now();
    timing.reset();

    std::cout << "\n=== Sphere Segmentation Processing ===" << std::endl;

    // Prepare volume data structure
    VolumeData volume;
    size_t voxels = static_cast<size_t>(width) * height * depth;
    size_t bytesPerVoxel = isSigned ? sizeof(int16_t) : sizeof(uint16_t);
    volume.width = width;
    volume.height = height;
    volume.depth = depth;
    volume.is_signed = isSigned;

    // Copy input data to internal buffer
    volume.data = std::make_unique<uint8_t[]>(voxels * bytesPerVoxel);
    std::memcpy(volume.data.get(), volumeData, voxels * bytesPerVoxel);

    // Initialize AI detector
    if (!pImpl->detector)
    {
        pImpl->detector = std::make_unique<YoloOnnxDetector>(
            config.modelPath, config.useCuda, config.numThreads, width, height);
    }
    pImpl->useCuda = config.useCuda;
    pImpl->numThreads = config.numThreads;

    // Step 1: Auto focus (aligned to Python detect_balls_precise)
    std::cout << "\nStep 1: Auto focus..." << std::endl;
    auto focusStart = clock::now();
    int targetLayer = computeAutoFocus(volume);
    timing.focusTime = std::chrono::duration<double>(clock::now() - focusStart).count();

    // Step 2: Object detection
    std::cout << "\nStep 2: Object detection..." << std::endl;
    auto aiStart = clock::now();
    auto detections = detectObjects(volume, targetLayer, config.modelPath);
    timing.aiInferTime = std::chrono::duration<double>(clock::now() - aiStart).count();

    // Step 3: Apply corrections and filtering
    std::cout << "\nStep 3: Tilt correction and filtering..." << std::endl;
    detections = applyTiltCorrection(volume, detections);
    detections = filterByClass3BigBox(detections, width, height);

    if (detections.empty())
    {
        std::cout << "No valid detections found" << std::endl;
        return false;
    }

    // Step 4: Calculate ROI for cropping (aligned to Python)
    std::cout << "\nStep 4: Calculate cropping region..." << std::endl;
    int min_x = width, min_y = height;
    int max_x = 0, max_y = 0;

    for (const auto& det : detections)
    {
        if (det.classId != 3)  // Skip class-3 boxes for ROI calculation
        {
            min_x = (min_x < det.box.x) ? min_x : det.box.x;
            min_y = (min_y < det.box.y) ? min_y : det.box.y;
            max_x = (max_x > det.box.x + det.box.width) ? max_x : det.box.x + det.box.width;
            max_y = (max_y > det.box.y + det.box.height) ? max_y : det.box.y + det.box.height;
        }
    }

    if (max_x <= min_x || max_y <= min_y)
    {
        std::cout << "Cannot calculate valid cropping region" << std::endl;
        return false;
    }

    std::cout << "Cropping region: X[" << min_x << ":" << max_x << "], Y[" << min_y << ":" << max_y << "]" << std::endl;
    std::cout << "Cropped size: " << depth << "x" << (max_y - min_y) << "x" << (max_x - min_x) << std::endl;

    // Step 5: Crop volume data (aligned to Python: volume_data = volume_data[:, ry0:ry1, rx0:rx1])
    std::cout << "\nStep 5: Crop volume data..." << std::endl;
    auto cropStart = clock::now();

    int rx0 = min_x, ry0 = min_y;
    int rx1 = max_x, ry1 = max_y;

    // Create cropped volume
    VolumeData cropped_volume;
    cropped_volume.width = rx1 - rx0;
    cropped_volume.height = ry1 - ry0;
    cropped_volume.depth = depth;
    cropped_volume.is_signed = isSigned;

    size_t cropped_voxels = static_cast<size_t>(cropped_volume.width) * cropped_volume.height * cropped_volume.depth;
    cropped_volume.data = std::make_unique<uint8_t[]>(cropped_voxels * bytesPerVoxel);

    // Copy cropped data: volume_data[:, ry0:ry1, rx0:rx1]
    for (int z = 0; z < depth; ++z)
    {
        for (int y = 0; y < cropped_volume.height; ++y)
        {
            for (int x = 0; x < cropped_volume.width; ++x)
            {
                size_t src_idx = static_cast<size_t>(z) * width * height +
                               static_cast<size_t>(ry0 + y) * width + (rx0 + x);
                size_t dst_idx = static_cast<size_t>(z) * cropped_volume.width * cropped_volume.height +
                               static_cast<size_t>(y) * cropped_volume.width + x;

                if (isSigned)
                {
                    reinterpret_cast<int16_t*>(cropped_volume.data.get())[dst_idx] =
                        reinterpret_cast<const int16_t*>(volume.data.get())[src_idx];
                }
                else
                {
                    reinterpret_cast<uint16_t*>(cropped_volume.data.get())[dst_idx] =
                        reinterpret_cast<const uint16_t*>(volume.data.get())[src_idx];
                }
            }
        }
    }

    auto cropTime = std::chrono::duration<double>(clock::now() - cropStart).count();
    std::cout << "Data cropping completed, time: " << cropTime << "s" << std::endl;
    std::cout << "Data compression ratio: " << (100.0 * cropped_voxels / voxels) << "%" << std::endl;

    // Convert detections to local coordinates (aligned to Python)
    std::vector<Detection> local_detections;
    local_detections.reserve(detections.size());
    for (const auto& det : detections)
    {
        Detection local_det = det;
        local_det.box.x -= rx0;
        local_det.box.y -= ry0;
        local_detections.push_back(local_det);
    }

    std::cout << "Detection boxes converted to local coordinates" << std::endl;

    // Step 6: 3D segmentation using cropped volume (aligned to Python)
    std::cout << "\nStep 6: 3D segmentation..." << std::endl;
    auto segStart = clock::now();
    auto sphereMasks = segmentSpheresBinary(cropped_volume, local_detections, targetLayer);
    timing.segmentationTime = std::chrono::duration<double>(clock::now() - segStart).count();

    // Step 7: Generate final result using cropped volume
    std::cout << "\nStep 7: Generate final result..." << std::endl;
    auto saveStart = clock::now();
    bool success = generateSegmentationResult(cropped_volume, sphereMasks, targetLayer, "", result);
    timing.saveTime = std::chrono::duration<double>(clock::now() - saveStart).count();

    timing.totalTime = std::chrono::duration<double>(clock::now() - totalStart).count();

    // Time statistics
    std::cout << "\nTime statistics:" << std::endl;
    std::cout << "  Auto focus: " << timing.focusTime << "s" << std::endl;
    std::cout << "  AI inference: " << timing.aiInferTime << "s" << std::endl;
    std::cout << "  3D segmentation: " << timing.segmentationTime << "s" << std::endl;
    std::cout << "  Data saving: " << timing.saveTime << "s" << std::endl;
    std::cout << "  Total time: " << timing.totalTime << "s" << std::endl;

    std::cout << "\nSphere segmentation completed" << std::endl;
    std::cout << "Final output size: " << cropped_volume.width << "x" << cropped_volume.height << "x" << cropped_volume.depth << std::endl;
    std::cout << "=========================================" << std::endl;

    return success;
}

bool SphereSeg3D::processVolumeFromDirectory(const std::string& inputPath,
                                            const std::string& outputPath,
                                            const ProcessingConfig& config)
{
    // Load volume data from directory
    VolumeData volume;
    if (!load_volume_from_slices(inputPath, volume))
    {
        std::cerr << "Failed to load volume from directory: " << inputPath << std::endl;
        return false;
    }

    // Process the volume
    SegmentationResult result;
    bool success = processVolumeFromMemory(volume.data.get(),
                                          volume.width,
                                          volume.height,
                                          volume.depth,
                                          volume.is_signed,
                                          config,
                                          result);

    if (!success)
    {
        std::cerr << "Volume processing failed" << std::endl;
        return false;
    }

    // Save results to output directory
    std::cout << "\nDLL processing completed successfully" << std::endl;
    std::cout << "Result dimensions: " << result.width << "x" << result.height << "x" << result.depth << std::endl;
    return true;
}

std::optional<VolumeData> SphereSeg3D::loadVolumeFromDirectory(const std::string& inputPath)
{
    VolumeData volume;
    if (!load_volume_from_slices(inputPath, volume))
    {
        return std::nullopt;
    }
    return std::move(volume);
}


// Main processing pipeline (aligned to Python main)
bool SphereSeg3D::process_volume(const std::string& input_path, const std::string& output_path, const std::string& model_path)
{
    using clock = std::chrono::high_resolution_clock;
    auto total_start = clock::now();

    std::cout << "SphereSeg3D start" << std::endl;

    // 1. Load volume data
    std::cout << "Loading volume..." << std::endl;
    auto load_start = clock::now();

    VolumeData volume;
    if (!load_volume_from_slices(input_path, volume))
    {
        std::cerr << "Load volume failed" << std::endl;
        return false;
    }

    auto load_end = clock::now();
    timing.dataLoadTime = std::chrono::duration<double>(load_end - load_start).count();

    std::cout << volume.depth << " x " << volume.height << " x " << volume.width << std::endl;
    std::cout << (volume.is_signed ? "int16" : "uint16") << std::endl;

    // Helpers
    auto get_slice_mat = [&](int z) -> cv::Mat
    {
        int W = volume.width;
        int H = volume.height;
        size_t slice_voxels = static_cast<size_t>(W) * H;
        size_t offset = static_cast<size_t>(z) * slice_voxels;
        if (volume.is_signed)
        {
            const int16_t* ptr = reinterpret_cast<const int16_t*>(volume.data.get()) + offset;
            return cv::Mat(H, W, CV_16SC1, const_cast<int16_t*>(ptr));
        }
        else
        {
            const uint16_t* ptr = reinterpret_cast<const uint16_t*>(volume.data.get()) + offset;
            return cv::Mat(H, W, CV_16UC1, const_cast<uint16_t*>(ptr));
        }
    };

    auto tenengrad = [&](const cv::Mat& img16) -> double
    {
        cv::Mat img8;
        double minv = 0.0, maxv = 0.0;
        cv::minMaxLoc(img16, &minv, &maxv);
        double scale = (maxv > minv) ? 255.0 / (maxv - minv) : 1.0;
        cv::Mat norm8;
        img16.convertTo(norm8, CV_8U, scale, -minv * scale);
        cv::Mat gx, gy;
        cv::Sobel(norm8, gx, CV_64F, 1, 0, 3);
        cv::Sobel(norm8, gy, CV_64F, 0, 1, 3);
        cv::Mat fm;
        cv::multiply(gx, gx, gx);
        cv::multiply(gy, gy, gy);
        fm = gx + gy;
        return cv::mean(fm)[0];
    };

    // 2. Auto focus (compute scores and peaks)
    std::cout << "Auto focus..." << std::endl;
    auto focus_start = clock::now();

    std::vector<double> scores;
    scores.reserve(static_cast<size_t>(volume.depth));
    for (int z = 0; z < volume.depth; ++z)
    {
        scores.push_back(tenengrad(get_slice_mat(z)));
    }

    auto compute_peaks = [&](const std::vector<double>& s) -> std::vector<int>
    {
        int n = static_cast<int>(s.size());
        if (n < 3)
        {
            int mid = (n > 0) ? n / 2 : 0;
            return { mid };
        }
        int max_k = n / 2;
        std::vector<int> arr_rowsum(max_k, 0);
        for (int k = 1; k <= max_k; ++k)
        {
            int cnt = 0;
            for (int i = k; i < n - k; ++i)
            {
                if (s[i] > s[i - k] && s[i] > s[i + k])
                {
                    ++cnt;
                }
            }
            arr_rowsum[k - 1] = -cnt;
        }
        int min_index = 0;
        for (int i = 1; i < max_k; ++i)
        {
            if (arr_rowsum[i] < arr_rowsum[min_index])
            {
                min_index = i;
            }
        }
        int max_window_length = min_index + 1;
        std::vector<char> candidate(n, 1);
        std::vector<int> p_data(n, 0);
        for (int k = 1; k <= max_window_length; ++k)
        {
            for (int i = k; i < n - k; ++i)
            {
                if (!candidate[i])
                {
                    continue;
                }
                bool is_peak = (s[i] > s[i - k]) && (s[i] > s[i + k]);
                if (is_peak)
                {
                    p_data[i] += 1;
                }
                else
                {
                    candidate[i] = 0;
                }
            }
            for (int i = 0; i <= k; ++i) candidate[i] = 0;
            for (int i = n - k - 1; i < n; ++i) if (i >= 0) candidate[i] = 0;
        }
        std::vector<int> peaks;
        for (int i = 0; i < n; ++i)
        {
            if (p_data[i] == max_window_length)
            {
                peaks.push_back(i);
            }
        }
        if (peaks.empty())
        {
            peaks.push_back(n / 2);
        }
        if (scores.front() >= *std::max_element(scores.begin(), scores.end()))
        {
            peaks.push_back(0);
        }
        if (scores.back() >= *std::max_element(scores.begin(), scores.end()))
        {
            peaks.push_back(n - 1);
        }
        std::sort(peaks.begin(), peaks.end());
        peaks.erase(std::unique(peaks.begin(), peaks.end()), peaks.end());
        return peaks;
    };

    std::vector<int> peak_indices = compute_peaks(scores);
    int best_idx = static_cast<int>(std::max_element(scores.begin(), scores.end()) - scores.begin());

    auto focus_end = clock::now();
    timing.focusTime = std::chrono::duration<double>(focus_end - focus_start).count();

    std::cout << "Auto focus done in " << std::fixed << std::setprecision(2) << timing.focusTime << " s" << std::endl;
    std::cout << "Peaks: " << peak_indices.size() << std::endl;
    {
        std::cout << "Peak indices: [";
        for (size_t i = 0; i < peak_indices.size(); ++i)
        {
            std::cout << peak_indices[i];
            if (i + 1 < peak_indices.size()) std::cout << ", ";
        }
        std::cout << "]" << std::endl;
    }

    // 3. AI detection and select target layer by most detections and best focus score
    std::vector<Detection> detections;
    int target_layer = best_idx;
    if (!model_path.empty())
    {
        if (!pImpl->detector)
        {
            // Use CPU by default on this environment
            pImpl->detector = std::make_unique<YoloOnnxDetector>(model_path, false, 1);
        }

        auto ai_start = clock::now();

        size_t best_count = 0;
        double best_focus = -1.0;
        int chosen_layer = peak_indices.front();
        std::vector<Detection> chosen_dets;

        for (int z : peak_indices)
        {
            cv::Mat slice = get_slice_mat(z);
            double minv = 0.0, maxv = 0.0;
            cv::minMaxLoc(slice, &minv, &maxv);
            double scale = (maxv > minv) ? 255.0 / (maxv - minv) : 1.0;
            cv::Mat slice_u8;
            slice.convertTo(slice_u8, CV_8U, scale, -minv * scale);
            auto dets = pImpl->detector->Detect(slice_u8);
            for (auto& d : dets) d.z = z;
            size_t cnt = dets.size();
            double fscore = scores[z];
            if (cnt > best_count || (cnt == best_count && fscore > best_focus))
            {
                best_count = cnt;
                best_focus = fscore;
                chosen_layer = z;
                chosen_dets = dets;
            }
        }

        target_layer = chosen_layer;
        detections = std::move(chosen_dets);

        auto ai_end = clock::now();
        timing.aiInferTime = std::chrono::duration<double>(ai_end - ai_start).count();

        std::cout << "AI inference time: " << timing.aiInferTime << " s" << std::endl;
        std::cout << "Detections: " << detections.size() << std::endl;
        std::cout << "Target layer: " << target_layer << ", Detections: " << detections.size() << std::endl;

        // Tilt correction + grid flatten + re-detect
        detections = applyTiltCorrection(volume, detections);

        // Filter by class-3 big box
        detections = filterByClass3BigBox(detections, volume.width, volume.height);

        // Save detection overlay like Python (target layer)
        {
            cv::Mat slice16 = get_slice_mat(target_layer);
            double minv = 0.0, maxv = 0.0;
            cv::minMaxLoc(slice16, &minv, &maxv);
            double scale = (maxv > minv) ? 255.0 / (maxv - minv) : 1.0;
            cv::Mat img8; slice16.convertTo(img8, volume.is_signed ? CV_8U : CV_8U, scale, -minv * scale);
            cv::Mat img3; cv::cvtColor(img8, img3, cv::COLOR_GRAY2BGR);
            auto color_of = [](int cls)->cv::Scalar
            {
                switch (cls)
                {
                    case 0: return cv::Scalar(0, 0, 255);   // red
                    case 1: return cv::Scalar(0, 255, 0);   // green
                    case 2: return cv::Scalar(255, 0, 0);   // blue
                    case 3: return cv::Scalar(0, 255, 255); // yellow
                    default: return cv::Scalar(255, 255, 255);
                }
            };
            for (const auto& d : detections)
            {
                cv::rectangle(img3, d.box, color_of(d.classId), 1);
            }
            // Detection visualization removed - handled by application if needed
        }

        // Print Z list in Python-like order (after filter)
        if (!detections.empty())
        {
            std::cout << "Z list (final): [";
            for (size_t i = 0; i < detections.size(); ++i)
            {
                std::cout << detections[i].z;
                if (i + 1 < detections.size()) std::cout << ", ";
            }
            std::cout << "]" << std::endl;
        }

        // Crop volume by minimal bounding rectangle of detections (XY only)
        int crop_x0 = 0, crop_y0 = 0, crop_x1 = volume.width, crop_y1 = volume.height;
        if (!detections.empty())
        {
            int xs1 = volume.width, ys1 = volume.height, xs2 = 0, ys2 = 0;
            for (const auto& d : detections)
            {
                xs1 = std::min(xs1, d.box.x);
                ys1 = std::min(ys1, d.box.y);
                xs2 = std::max(xs2, d.box.x + d.box.width);
                ys2 = std::max(ys2, d.box.y + d.box.height);
            }
            crop_x0 = std::max(0, xs1);
            crop_y0 = std::max(0, ys1);
            crop_x1 = std::min(volume.width, xs2);
            crop_y1 = std::min(volume.height, ys2);
            if (crop_x1 <= crop_x0 || crop_y1 <= crop_y0)
            {
                crop_x0 = 0; crop_y0 = 0; crop_x1 = volume.width; crop_y1 = volume.height;
            }
        }
        std::cout << "Crop rect: x0=" << crop_x0 << ", y0=" << crop_y0
                  << ", x1=" << crop_x1 << ", y1=" << crop_y1
                  << ", w=" << (crop_x1 - crop_x0) << ", h=" << (crop_y1 - crop_y0)
                  << std::endl;

        if (crop_x0 != 0 || crop_y0 != 0 || crop_x1 != volume.width || crop_y1 != volume.height)
        {
            int newW = crop_x1 - crop_x0;
            int newH = crop_y1 - crop_y0;
            int Z = volume.depth;
            size_t bytesPerVoxel = volume.is_signed ? sizeof(int16_t) : sizeof(uint16_t);
            size_t newVoxels = static_cast<size_t>(newW) * newH * Z;
            auto newData = std::make_unique<uint8_t[]>(newVoxels * bytesPerVoxel);

            for (int z = 0; z < Z; ++z)
            {
                for (int y = 0; y < newH; ++y)
                {
                    size_t dstIdx = static_cast<size_t>(z) * newW * newH + static_cast<size_t>(y) * newW;
                    size_t srcIdx = static_cast<size_t>(z) * volume.width * volume.height + static_cast<size_t>(y + crop_y0) * volume.width + crop_x0;
                    if (volume.is_signed)
                    {
                        const int16_t* src = reinterpret_cast<const int16_t*>(volume.data.get()) + srcIdx;
                        int16_t* dst = reinterpret_cast<int16_t*>(newData.get()) + dstIdx;
                        std::memcpy(dst, src, static_cast<size_t>(newW) * bytesPerVoxel);
                    }
                    else
                    {
                        const uint16_t* src = reinterpret_cast<const uint16_t*>(volume.data.get()) + srcIdx;
                        uint16_t* dst = reinterpret_cast<uint16_t*>(newData.get()) + dstIdx;
                        std::memcpy(dst, src, static_cast<size_t>(newW) * bytesPerVoxel);
                    }
                }
            }
            volume.data = std::move(newData);
            volume.width = newW;
            volume.height = newH;

            for (auto& d : detections)
            {
                d.box.x -= crop_x0;
                d.box.y -= crop_y0;
            }
        }
    }

    // 4. 3D Segmentation
    std::cout << "Start 3D segmentation..." << std::endl;
    auto seg_start = clock::now();

    auto sphere_masks = segmentSpheresBinary(volume, detections, target_layer);

    auto seg_end = clock::now();
    timing.segmentationTime = std::chrono::duration<double>(seg_end - seg_start).count();

    std::cout << "3D segmentation time: " << timing.segmentationTime << " s" << std::endl;
    std::cout << "Spheres: " << sphere_masks.size() << std::endl;

    // 5. Save results
    std::cout << "Saving 3D segmentation..." << std::endl;
    auto save_start = clock::now();

    SegmentationResult result;
    if (!generateSegmentationResult(volume, sphere_masks, target_layer, output_path, result))
    {
        std::cerr << "Save segmentation failed" << std::endl;
        return false;
    }

    auto save_end = clock::now();
    timing.saveTime = std::chrono::duration<double>(save_end - save_start).count();

    std::cout << "Save done in: " << timing.saveTime << " s" << std::endl;

    // 6. Stats
    auto total_end = clock::now();
    timing.totalTime = std::chrono::duration<double>(total_end - total_start).count();

    std::cout << "\nTiming: " << std::endl;
    std::cout << "  load: " << timing.dataLoadTime << " s" << std::endl;
    std::cout << "  focus: " << timing.focusTime << " s" << std::endl;
    if (!model_path.empty())
    {
        std::cout << "  ai: " << timing.aiInferTime << " s" << std::endl;
    }
    std::cout << "  seg: " << timing.segmentationTime << " s" << std::endl;
    std::cout << "  save: " << timing.saveTime << " s" << std::endl;
    std::cout << "  total: " << timing.totalTime << " s" << std::endl;

    return true;
}

// Load volume from slices (compatible with Python ImageParam.ini)
bool SphereSeg3D::load_volume_from_slices(const std::string& input_path, VolumeData& volume)
{
    namespace fs = std::filesystem;

    std::cout << "Loading volume from: " << input_path << std::endl;

    if (!fs::exists(input_path) || !fs::is_directory(input_path))
    {
        std::cerr << "Input path not directory: " << input_path << std::endl;
        return false;
    }

    // Debug: List files in directory
    std::cout << "Files in input directory:" << std::endl;
    int fileCount = 0;
    for (const auto& entry : fs::directory_iterator(input_path))
    {
        std::cout << "  " << entry.path().filename() << std::endl;
        fileCount++;
        if (fileCount > 10) // Limit output for large directories
        {
            std::cout << "  ... and more files" << std::endl;
            break;
        }
    }

    fs::path ini_path = fs::path(input_path) / "ImageParam.ini";
    bool has_ini = fs::exists(ini_path);

    std::map<std::string, std::map<std::string, std::string>> kv;
    if (has_ini)
    {
        // Parse INI with optional sections
        std::ifstream ifs(ini_path);
        std::string line;
        std::string section;
        while (std::getline(ifs, line))
        {
            if (line.empty())
            {
                continue;
            }
            if (line.front() == '[' && line.back() == ']')
            {
                section = line.substr(1, line.size() - 2);
                continue;
            }
            auto pos = line.find('=');
            if (pos == std::string::npos)
            {
                continue;
            }
            std::string key = line.substr(0, pos);
            std::string val = line.substr(pos + 1);
            // trim spaces
            key.erase(0, key.find_first_not_of(" \t\r"));
            key.erase(key.find_last_not_of(" \t\r") + 1);
            val.erase(0, val.find_first_not_of(" \t\r"));
            val.erase(val.find_last_not_of(" \t\r") + 1);
            kv[section][key] = val;
        }
    }

    int W = 0, H = 0;
    int beginIndex = 0, endIndex = -1;
    std::string nameTemplate = "Slice_%d.raw";
    bool isSigned = true;

    if (has_ini)
    {
        if (kv.count("RawImageInfo"))
        {
            auto& raw = kv["RawImageInfo"];
            if (raw.count("Width")) W = std::stoi(raw["Width"]);
            if (raw.count("Height")) H = std::stoi(raw["Height"]);
            int bits = raw.count("BitsAllocated") ? std::stoi(raw["BitsAllocated"]) : 16;
            int pixelRep = raw.count("PixelRepresentation") ? std::stoi(raw["PixelRepresentation"]) : 1;
            isSigned = (bits == 16 && pixelRep == 1);
        }

        if (kv.count("FileModule"))
        {
            auto& fm = kv["FileModule"];
            if (fm.count("BeginIndex")) beginIndex = std::stoi(fm["BeginIndex"]);
            if (fm.count("EndIndex")) endIndex = std::stoi(fm["EndIndex"]);
            if (fm.count("NameTemplate")) nameTemplate = fm["NameTemplate"];
        }

        // Fallback for flat keys
        if (W <= 0 || H <= 0)
        {
            if (kv.count("") && kv[""] .count("Width")) W = std::stoi(kv[""]["Width"]);
            if (kv.count("") && kv[""] .count("Height")) H = std::stoi(kv[""]["Height"]);
            if (kv.count("") && kv[""] .count("DataType"))
            {
                std::string dt = kv[""]["DataType"];
                isSigned = (dt == "int16");
            }
            if (kv.count("") && kv[""] .count("Z0")) beginIndex = std::stoi(kv[""]["Z0"]);
            if (kv.count("") && kv[""] .count("Z1")) endIndex = std::stoi(kv[""]["Z1"]);
        }
    }

    // If no ini, try PNG sequence (8-bit)
    if (!has_ini)
    {
        std::vector<fs::path> pngs;
        for (auto& p : fs::directory_iterator(input_path))
        {
            if (!p.is_regular_file()) continue;
            auto ext = p.path().extension().string();
            std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
            if (ext == ".png") pngs.push_back(p.path());
        }
        std::sort(pngs.begin(), pngs.end());
        if (pngs.empty())
        {
            std::cerr << "Neither ImageParam.ini nor PNG sequence found" << std::endl;
            return false;
        }
        // read first to get size
        cv::Mat img0 = cv::imread(pngs.front().string(), cv::IMREAD_UNCHANGED);
        if (img0.empty()) { std::cerr << "Failed to read PNG: " << pngs.front() << std::endl; return false; }
        if (img0.channels() == 3) cv::cvtColor(img0, img0, cv::COLOR_BGR2GRAY);
        if (img0.depth() != CV_8U)
        {
            cv::Mat tmp8; double minv=0, maxv=0; cv::minMaxLoc(img0, &minv, &maxv);
            double scale = (maxv>minv) ? 255.0/(maxv-minv) : 1.0; img0.convertTo(tmp8, CV_8U, scale, -minv*scale); img0 = tmp8;
        }
        H = img0.rows; W = img0.cols; int D = static_cast<int>(pngs.size());
        size_t voxels = static_cast<size_t>(W) * H * D; size_t bytesPerVoxel = sizeof(uint16_t);
        volume.width = W; volume.height = H; volume.depth = D; volume.is_signed = false;
        volume.data = std::make_unique<uint8_t[]>(voxels * bytesPerVoxel);
        for (int z = 0; z < D; ++z)
        {
            cv::Mat im = cv::imread(pngs[static_cast<size_t>(z)].string(), cv::IMREAD_UNCHANGED);
            if (im.empty()) { std::cerr << "Failed to read PNG: " << pngs[static_cast<size_t>(z)] << std::endl; volume.data.reset(); return false; }
            if (im.channels() == 3) cv::cvtColor(im, im, cv::COLOR_BGR2GRAY);
            if (im.depth() != CV_8U)
            {
                cv::Mat tmp8; double minv=0, maxv=0; cv::minMaxLoc(im, &minv, &maxv);
                double scale = (maxv>minv) ? 255.0/(maxv-minv) : 1.0; im.convertTo(tmp8, CV_8U, scale, -minv*scale); im = tmp8;
            }
            // upscale to 16-bit to align with pipeline typing
            cv::Mat im16; im.convertTo(im16, CV_16U, 257.0);
            size_t sliceBytes = static_cast<size_t>(W) * H * bytesPerVoxel;
            size_t offset = static_cast<size_t>(z) * sliceBytes;
            std::memcpy(volume.data.get() + offset, im16.data, sliceBytes);
        }
        return true;
    }

    if (W <= 0 || H <= 0 || endIndex < beginIndex)
    {
        std::cerr << "Invalid parameters in ImageParam.ini" << std::endl;
        return false;
    }

    int D = endIndex - beginIndex + 1;
    size_t voxels = static_cast<size_t>(W) * H * D;
    size_t bytesPerVoxel = isSigned ? sizeof(int16_t) : sizeof(uint16_t);

    volume.width = W;
    volume.height = H;
    volume.depth = D;
    volume.is_signed = isSigned;
    volume.data = std::make_unique<uint8_t[]>(voxels * bytesPerVoxel);

    auto format_name = [&](int idx) -> std::string
    {
        char buf[256];
        std::snprintf(buf, sizeof(buf), nameTemplate.c_str(), idx);
        return std::string(buf);
    };

    for (int z = beginIndex; z <= endIndex; ++z)
    {
        fs::path slice_path = fs::path(input_path) / format_name(z);
        if (!fs::exists(slice_path))
        {
            std::cerr << "Slice not found: " << slice_path << std::endl;
            volume.data.reset();
            return false;
        }
        std::ifstream sifs(slice_path, std::ios::binary);
        size_t sliceBytes = static_cast<size_t>(W) * H * bytesPerVoxel;
        size_t offset = static_cast<size_t>(z - beginIndex) * sliceBytes;
        sifs.read(reinterpret_cast<char*>(volume.data.get()) + offset, sliceBytes);
        if (!sifs.good())
        {
            std::cerr << "Read slice failed: " << slice_path << std::endl;
            volume.data.reset();
            return false;
        }
    }

    // Debug: Print data statistics
    std::cout << "Volume loaded successfully: " << W << "x" << H << "x" << D << std::endl;
    std::cout << "Data type: " << (volume.is_signed ? "int16" : "uint16") << std::endl;

    // Calculate min/max values for first slice
    if (volume.data && W > 0 && H > 0)
    {
        if (volume.is_signed)
        {
            const int16_t* data = reinterpret_cast<const int16_t*>(volume.data.get());
            int16_t minVal = data[0], maxVal = data[0];
            size_t sampleSize = std::min(static_cast<size_t>(W * H), static_cast<size_t>(10000));
            for (size_t i = 0; i < sampleSize; ++i)
            {
                minVal = std::min(minVal, data[i]);
                maxVal = std::max(maxVal, data[i]);
            }
            std::cout << "First slice value range: [" << minVal << ", " << maxVal << "]" << std::endl;
        }
        else
        {
            const uint16_t* data = reinterpret_cast<const uint16_t*>(volume.data.get());
            uint16_t minVal = data[0], maxVal = data[0];
            size_t sampleSize = std::min(static_cast<size_t>(W * H), static_cast<size_t>(10000));
            for (size_t i = 0; i < sampleSize; ++i)
            {
                minVal = std::min(minVal, data[i]);
                maxVal = std::max(maxVal, data[i]);
            }
            std::cout << "First slice value range: [" << minVal << ", " << maxVal << "]" << std::endl;
        }
    }

    return true;
}

// AMPD peak detection algorithm (aligned to Python version)
std::vector<int> SphereSeg3D::AMPD(const std::vector<double>& data)
{
    int n = static_cast<int>(data.size());
    if (n < 3)
    {
        return {};
    }

    // Step 1: Find optimal window length
    int max_k = n / 2;
    std::vector<int> arr_rowsum(max_k, 0);

    for (int k = 1; k <= max_k; ++k)
    {
        int count_peak = 0;
        for (int i = k; i < n - k; ++i)
        {
            if (data[i] > data[i - k] && data[i] > data[i + k])
            {
                count_peak++;
            }
        }
        arr_rowsum[k - 1] = -count_peak;  // Keep original logic (negative count)
    }

    int min_index = static_cast<int>(std::min_element(arr_rowsum.begin(), arr_rowsum.end()) - arr_rowsum.begin());
    int max_window_length = min_index + 1;

    // Step 2: Peak detection with candidate mechanism
    std::vector<bool> candidate_mask(n, true);
    std::vector<int> p_data(n, 0);

    for (int k = 1; k <= max_window_length; ++k)
    {
        for (int i = k; i < n - k; ++i)
        {
            if (candidate_mask[i] && data[i] > data[i - k] && data[i] > data[i + k])
            {
                p_data[i]++;
            }
        }

        // Remove edge candidates
        for (int i = 0; i <= k; ++i)
        {
            candidate_mask[i] = false;
        }
        for (int i = n - k - 1; i < n; ++i)
        {
            candidate_mask[i] = false;
        }
    }

    // Return all positions that satisfy the condition
    std::vector<int> peaks;
    for (int i = 0; i < n; ++i)
    {
        if (p_data[i] == max_window_length)
        {
            peaks.push_back(i);
        }
    }

    return peaks;
}

// Compute optimal focus layer using gradient analysis and peak detection
// Parameters:
//   volume: 3D volume data for analysis
// Returns: Best focus layer index
int SphereSeg3D::computeAutoFocus(const VolumeData& volume)
{
    std::cout << "\n=== Auto Focus Analysis ===" << std::endl;

    // Delegate focus score computation and AMPD to FocusAnalyzer for modularity
    ss3d::FocusAnalyzer fa;
    std::vector<double> focus_scores = fa.tenengradFocusScores(volume);

    // Find global best index
    int global_best_idx = static_cast<int>(std::max_element(focus_scores.begin(), focus_scores.end()) - focus_scores.begin());
    double max_score = focus_scores[global_best_idx];

    std::cout << "Global best focus layer: " << global_best_idx << " (score: " << max_score << ")" << std::endl;

    // Apply AMPD peak detection
    std::vector<int> peak_idx_list = fa.AMPD(focus_scores);

    // Check first layer
    if (focus_scores[0] >= max_score)
    {
        peak_idx_list.push_back(0);
    }

    // Check last layer
    if (focus_scores[volume.depth - 1] >= max_score)
    {
        peak_idx_list.push_back(volume.depth - 1);
    }

    // Sort and remove duplicates
    std::sort(peak_idx_list.begin(), peak_idx_list.end());
    peak_idx_list.erase(std::unique(peak_idx_list.begin(), peak_idx_list.end()), peak_idx_list.end());

    std::cout << "AMPD detected peaks: ";
    for (size_t i = 0; i < peak_idx_list.size(); ++i)
    {
        std::cout << peak_idx_list[i];
        if (i < peak_idx_list.size() - 1) std::cout << ", ";
    }
    std::cout << " (total: " << peak_idx_list.size() << " peaks)" << std::endl;

    // Print focus scores around the best region for debugging
    int start = std::max(0, global_best_idx - 10);
    int end = std::min(volume.depth, global_best_idx + 11);
    std::cout << "Focus scores around best layer [" << start << "-" << (end-1) << "]: ";
    for (int i = start; i < end; ++i)
    {
        std::cout << i << ":" << std::fixed << std::setprecision(1) << focus_scores[i];
        if (i < end - 1) std::cout << ", ";
    }
    std::cout << std::endl;

    // Multi-peak selection logic (aligned to Python version)
    int selected_layer = global_best_idx;

    if (peak_idx_list.size() > 1)
    {
        std::cout << "Multiple peaks detected, using AI inference to select best layer..." << std::endl;

        // Test each peak with AI detection to find the one with most detections
        int best_detection_count = -1;
        int best_peak_layer = global_best_idx;

        for (int peak_layer : peak_idx_list)
        {
            std::cout << "Testing peak layer " << peak_layer << "..." << std::endl;

            // Quick AI detection test on this layer
            if (pImpl->detector)
            {
                cv::Mat test_slice = get_slice(peak_layer);

                // Normalize to 8U for detector
                double minv = 0.0, maxv = 0.0;
                cv::minMaxLoc(test_slice, &minv, &maxv);
                double scale = (maxv > minv) ? 255.0 / (maxv - minv) : 1.0;
                cv::Mat test_slice_u8;
                test_slice.convertTo(test_slice_u8, CV_8U, scale, -minv * scale);

                auto test_dets = pImpl->detector->Detect(test_slice_u8);
                int detection_count = static_cast<int>(test_dets.size());

                std::cout << "  Layer " << peak_layer << ": " << detection_count << " detections" << std::endl;

                if (detection_count > best_detection_count)
                {
                    best_detection_count = detection_count;
                    best_peak_layer = peak_layer;
                }
            }
        }

        if (best_detection_count > 0)
        {
            selected_layer = best_peak_layer;
            std::cout << "Selected layer " << selected_layer << " with " << best_detection_count << " detections" << std::endl;
        }
        else
        {
            // If no detections found in any peak, prefer the earlier peak (smaller z)
            selected_layer = *std::min_element(peak_idx_list.begin(), peak_idx_list.end());
            std::cout << "No detections in peaks, selecting earliest peak: " << selected_layer << std::endl;
        }
    }
    else if (peak_idx_list.size() == 1)
    {
        selected_layer = peak_idx_list[0];
        std::cout << "Single peak detected, using layer: " << selected_layer << std::endl;
    }

    std::cout << "Final selected target layer: " << selected_layer << std::endl;
    std::cout << "=========================" << std::endl;

    return selected_layer;
}

// AI detection via YoloOnnxDetector
std::vector<Detection> SphereSeg3D::detectObjects(const VolumeData& volume, int targetLayer, const std::string& modelPath)
{
    if (!pImpl->detector)
    {
        pImpl->detector = std::make_unique<YoloOnnxDetector>(modelPath, false, 1);
    }

    int W = volume.width;
    int H = volume.height;
    size_t slice_voxels = static_cast<size_t>(W) * H;
    size_t offset = static_cast<size_t>(targetLayer) * slice_voxels;

    cv::Mat slice;
    if (volume.is_signed)
    {
        const int16_t* ptr = reinterpret_cast<const int16_t*>(volume.data.get()) + offset;
        slice = cv::Mat(H, W, CV_16SC1, const_cast<int16_t*>(ptr));
    }
    else
    {
        const uint16_t* ptr = reinterpret_cast<const uint16_t*>(volume.data.get()) + offset;
        slice = cv::Mat(H, W, CV_16UC1, const_cast<uint16_t*>(ptr));
    }

    // Normalize to 8U for detector
    double minv = 0.0, maxv = 0.0;
    cv::minMaxLoc(slice, &minv, &maxv);
    double scale = (maxv > minv) ? 255.0 / (maxv - minv) : 1.0;
    cv::Mat slice_u8;
    slice.convertTo(slice_u8, CV_8U, scale, -minv * scale);

    // Debug: Print slice information
    std::cout << "Processing slice " << targetLayer << " for AI detection" << std::endl;
    std::cout << "Original slice range: [" << minv << ", " << maxv << "]" << std::endl;
    std::cout << "Normalized slice size: " << slice_u8.cols << "x" << slice_u8.rows << std::endl;

    // Check if normalized image has reasonable values
    double minv_u8, maxv_u8;
    cv::minMaxLoc(slice_u8, &minv_u8, &maxv_u8);
    std::cout << "Normalized slice range: [" << minv_u8 << ", " << maxv_u8 << "]" << std::endl;

    // Debug image saving removed - DLL focuses on processing only

    auto dets = pImpl->detector->Detect(slice_u8);
    std::cout << "Raw detections from AI model: " << dets.size() << std::endl;
    for (auto& d : dets)
    {
        d.z = targetLayer;
    }
    return dets;
}

// Batch AI detection via YoloOnnxDetector - optimized for multiple layers
std::vector<std::vector<Detection>> SphereSeg3D::detectObjectsBatch(const VolumeData& volume, const std::vector<int>& targetLayers, const std::string& modelPath)
{
    if (!pImpl->detector)
    {
        pImpl->detector = std::make_unique<YoloOnnxDetector>(modelPath, false, 1);
    }

    if (targetLayers.empty())
    {
        return {};
    }

    int W = volume.width;
    int H = volume.height;
    size_t slice_voxels = static_cast<size_t>(W * H);

    // Prepare batch of slices
    std::vector<cv::Mat> slices;
    slices.reserve(targetLayers.size());

    for (int targetLayer : targetLayers)
    {
        if (targetLayer < 0 || targetLayer >= volume.depth)
        {
            continue;
        }

        size_t offset = static_cast<size_t>(targetLayer) * slice_voxels;
        cv::Mat slice;

        if (volume.is_signed)
        {
            const int16_t* ptr = reinterpret_cast<const int16_t*>(volume.data.get()) + offset;
            slice = cv::Mat(H, W, CV_16SC1, const_cast<int16_t*>(ptr));
        }
        else
        {
            const uint16_t* ptr = reinterpret_cast<const uint16_t*>(volume.data.get()) + offset;
            slice = cv::Mat(H, W, CV_16UC1, const_cast<uint16_t*>(ptr));
        }

        // Normalize to 8U for detector
        double minv = 0.0, maxv = 0.0;
        cv::minMaxLoc(slice, &minv, &maxv);
        double scale = (maxv > minv) ? 255.0 / (maxv - minv) : 1.0;
        cv::Mat slice_u8;
        slice.convertTo(slice_u8, CV_8U, scale, -minv * scale);

        slices.push_back(slice_u8);
    }

    if (slices.empty())
    {
        return {};
    }

    // Batch detection for improved performance
    std::cout << "Batch processing " << slices.size() << " slices..." << std::endl;
    auto batch_results = pImpl->detector->DetectBatch(slices);

    // Set z coordinates for each detection
    std::vector<std::vector<Detection>> results;
    results.reserve(batch_results.size());

    for (size_t i = 0; i < batch_results.size() && i < targetLayers.size(); ++i)
    {
        auto dets = batch_results[i];
        for (auto& d : dets)
        {
            d.z = targetLayers[i];
        }
        results.push_back(std::move(dets));
        std::cout << "Layer " << targetLayers[i] << ": " << dets.size() << " detections" << std::endl;
    }

    return results;
}



// Filter by class-3 big box and keep only boxes whose center inside the big box
std::vector<Detection> SphereSeg3D::filterByClass3BigBox(const std::vector<Detection>& detections, int width, int height)
{
    if (detections.empty())
    {
        return {};
    }

    // Gather class-3 boxes
    std::vector<cv::Rect> big;
    for (const auto& d : detections)
    {
        if (d.classId == 3)
        {
            big.push_back(d.box);
        }
    }
    if (big.empty())
    {
        return detections;
    }

    // Global min/max over all detections (used to reset class-3 later)
    int g_xmin = width, g_ymin = height, g_xmax = 0, g_ymax = 0;
    for (const auto& d : detections)
    {
        g_xmin = std::min(g_xmin, d.box.x);
        g_ymin = std::min(g_ymin, d.box.y);
        g_xmax = std::max(g_xmax, d.box.x + d.box.width);
        g_ymax = std::max(g_ymax, d.box.y + d.box.height);
    }

    int bx1 = width, by1 = height, bx2 = 0, by2 = 0;
    for (const auto& r : big)
    {
        bx1 = std::min(bx1, r.x);
        by1 = std::min(by1, r.y);
        bx2 = std::max(bx2, r.x + r.width);
        by2 = std::max(by2, r.y + r.height);
    }
    bx1 = std::max(0, bx1); by1 = std::max(0, by1);
    bx2 = std::min(width, bx2); by2 = std::min(height, by2);

    std::vector<Detection> kept;
    kept.reserve(detections.size());
    for (const auto& d : detections)
    {
        int cx = d.box.x + d.box.width / 2;
        int cy = d.box.y + d.box.height / 2;
        if (cx >= bx1 && cy >= by1 && cx <= bx2 && cy <= by2)
        {
            kept.push_back(d);
        }
    }

    // Reset class-3 boxes to global min/max (align Python behavior)
    for (auto& d : kept)
    {
        if (d.classId == 3)
        {
            d.box = cv::Rect(g_xmin, g_ymin, std::max(0, g_xmax - g_xmin), std::max(0, g_ymax - g_ymin));
        }
    }
    return kept.empty() ? detections : kept;
}

// Apply tilt correction: plane fit on class-0 corner boxes, grid flatten, re-detect
std::vector<Detection> SphereSeg3D::applyTiltCorrection(const VolumeData& volume, std::vector<Detection>& detections)
{
    if (!pImpl->detector || detections.empty())
    {
        return detections;
    }

    int Z = volume.depth;
    int H = volume.height;
    int W = volume.width;

    // Select up to 4 class-0 boxes nearest to corners
    auto select_corner_boxes = [&](const std::vector<Detection>& dets) -> std::vector<Detection>
    {
        std::vector<Detection> c0;
        for (const auto& d : dets) if (d.classId == 0) c0.push_back(d);
        if (c0.empty()) return {};
        std::vector<cv::Point> corners = { {0,0}, {W-1,0}, {0,H-1}, {W-1,H-1} };
        std::vector<Detection> chosen;
        std::vector<int> used(c0.size(), 0);
        for (auto c : corners)
        {
            int best_i = -1; long long best_d = (1LL<<60);
            for (int i = 0; i < static_cast<int>(c0.size()); ++i)
            {
                if (used[i]) continue;
                const auto& r = c0[i].box;
                int cx = r.x + r.width / 2;
                int cy = r.y + r.height / 2;
                long long dist = 1LL * (cx - c.x) * (cx - c.x) + 1LL * (cy - c.y) * (cy - c.y);
                if (dist < best_d)
                {
                    best_d = dist;
                    best_i = i;
                }
            }
            if (best_i >= 0)
            {
                chosen.push_back(c0[best_i]);
                used[best_i] = 1;
            }
        }
        return chosen;
    };

    auto corner_boxes = select_corner_boxes(detections);
    if (corner_boxes.size() < 2)
    {
        return detections; // not enough to fit plane
    }

    auto get_slice = [&](int z) -> cv::Mat
    {
        size_t slice_voxels = static_cast<size_t>(W) * H;
        size_t offset = static_cast<size_t>(z) * slice_voxels;
        if (volume.is_signed)
        {
            const int16_t* ptr = reinterpret_cast<const int16_t*>(volume.data.get()) + offset;
            return cv::Mat(H, W, CV_16SC1, const_cast<int16_t*>(ptr));
        }
        else
        {
            const uint16_t* ptr = reinterpret_cast<const uint16_t*>(volume.data.get()) + offset;
            return cv::Mat(H, W, CV_16UC1, const_cast<uint16_t*>(ptr));
        }
    };

    auto tenengrad_score = [&](const cv::Mat& tile) -> double
    {
        cv::Mat tmp8;
        double minv = 0, maxv = 0;
        cv::minMaxLoc(tile, &minv, &maxv);
        double scale = (maxv > minv) ? 255.0 / (maxv - minv) : 1.0;
        cv::Mat norm8;
        tile.convertTo(norm8, CV_8U, scale, -minv * scale);
        cv::Mat gx, gy;
        cv::Sobel(norm8, gx, CV_64F, 1, 0, 3);
        cv::Sobel(norm8, gy, CV_64F, 0, 1, 3);
        cv::Mat fm = gx.mul(gx) + gy.mul(gy);
        return cv::mean(fm)[0];
    };

    auto best_z_for_cube = [&](int z_center, int x0, int y0, int side_xy, int side_z) -> int
    {
        int half_z = std::max(1, side_z / 2);
        int z0 = std::max(0, z_center - half_z);
        int z1 = std::min(Z, z0 + side_z);
        x0 = std::max(0, x0); y0 = std::max(0, y0);
        int x1 = std::min(W, x0 + side_xy);
        int y1 = std::min(H, y0 + side_xy);
        if (x1 <= x0 || y1 <= y0 || z1 <= z0)
        {
            return std::min(std::max(z_center, 0), Z - 1);
        }
        int best_z = z0; double best = -1e18;
        for (int z = z0; z < z1; ++z)
        {
            cv::Mat tile = get_slice(z)(cv::Rect(x0, y0, x1 - x0, y1 - y0));
            double sc = tenengrad_score(tile);
            if (sc > best)
            {
                best = sc; best_z = z;
            }
        }
        return best_z;
    };

    // Fit plane z = a*x + b*y + c using corner samples
    struct Pt { double x, y, z; };
    std::vector<Pt> pts;
    pts.reserve(corner_boxes.size());
    for (const auto& d : corner_boxes)
    {
        int x1 = d.box.x, y1 = d.box.y, x2 = d.box.x + d.box.width, y2 = d.box.y + d.box.height;
        int side0 = std::max(x2 - x1, y2 - y1);
        int side_xy = std::max(1, side0 * 2);
        int cx = (x1 + x2) / 2; int cy = (y1 + y2) / 2;
        int rx0 = std::max(0, cx - side_xy / 2);
        int ry0 = std::max(0, cy - side_xy / 2);
        int bestz = best_z_for_cube(d.z, rx0, ry0, side_xy, side_xy);
        pts.push_back({ static_cast<double>(cx), static_cast<double>(cy), static_cast<double>(bestz) });
    }

    // Least squares fit
    double A11 = 0, A12 = 0, A13 = 0, A22 = 0, A23 = 0, A33 = 0;
    double B1 = 0, B2 = 0, B3 = 0;
    std::cout << "Corner samples: ";
    for (size_t i = 0; i < pts.size(); ++i)
    {
        std::cout << "(" << pts[i].x << ", " << pts[i].y << ", " << pts[i].z << ")";
        if (i + 1 < pts.size()) std::cout << ", ";
    }
    std::cout << std::endl;
    for (const auto& p : pts)
    {
        A11 += p.x * p.x; A12 += p.x * p.y; A13 += p.x;
        A22 += p.y * p.y; A23 += p.y;      A33 += 1.0;
        B1  += p.x * p.z; B2  += p.y * p.z; B3  += p.z;
    }
    // Solve 3x3 (Gaussian elimination)
    double M[3][4] = {
        { A11, A12, A13, B1 },
        { A12, A22, A23, B2 },
        { A13, A23, A33, B3 }
    };
    for (int i = 0; i < 3; ++i)
    {
        int pivot = i;
        for (int r = i + 1; r < 3; ++r)
        {
            if (std::fabs(M[r][i]) > std::fabs(M[pivot][i])) pivot = r;
        }
        if (pivot != i) for (int c = i; c < 4; ++c) std::swap(M[i][c], M[pivot][c]);
        double div = (std::fabs(M[i][i]) < 1e-12) ? 1.0 : M[i][i];
        for (int c = i; c < 4; ++c) M[i][c] /= div;
        for (int r = 0; r < 3; ++r)
        {
            if (r == i) continue;
            double f = M[r][i];
            for (int c = i; c < 4; ++c) M[r][c] -= f * M[i][c];
        }
    }
    double a = M[0][3], b = M[1][3], c = M[2][3];
    std::cout << "Fitted plane: z = " << std::fixed << std::setprecision(4) << a
              << " x + " << b << " y + " << std::setprecision(2) << c << std::endl;

    // Build class-0 bounding rect
    int rx0 = W, ry0 = H, rx1 = 0, ry1 = 0;
    for (const auto& d : detections)
    {
        if (d.classId != 0) continue;
        rx0 = std::min(rx0, d.box.x);
        ry0 = std::min(ry0, d.box.y);
        rx1 = std::max(rx1, d.box.x + d.box.width);
        ry1 = std::max(ry1, d.box.y + d.box.height);
    }
    if (rx1 <= rx0 || ry1 <= ry0)
    {
        return detections;
    }

    // Grid flatten mosaic
    int grid_n = 4;
    int gw = std::max(1, (rx1 - rx0) / grid_n);
    int gh = std::max(1, (ry1 - ry0) / grid_n);
    cv::Mat mosaic(ry1 - ry0, rx1 - rx0, volume.is_signed ? CV_16SC1 : CV_16UC1);
    cv::Mat grid_z(grid_n, grid_n, CV_32S, cv::Scalar(0));
    for (int gy = 0; gy < grid_n; ++gy)
    {
        for (int gx = 0; gx < grid_n; ++gx)
        {
            int xs = rx0 + gx * gw;
            int xe = (gx < grid_n - 1) ? rx0 + (gx + 1) * gw : rx1;
            int ys = ry0 + gy * gh;
            int ye = (gy < grid_n - 1) ? ry0 + (gy + 1) * gh : ry1;
            int cx = (xs + xe) / 2;
            int cy = (ys + ye) / 2;
            int zi = static_cast<int>(std::round(a * cx + b * cy + c));
            zi = std::max(0, std::min(Z - 1, zi));
            grid_z.at<int>(gy, gx) = zi;
            get_slice(zi)(cv::Rect(xs, ys, xe - xs, ye - ys)).copyTo(mosaic(cv::Rect(xs - rx0, ys - ry0, xe - xs, ye - ys)));
        }
    }

    // Re-detect on mosaic (normalize to 8U)
    cv::Mat mosaic8;
    {
        double minv = 0.0, maxv = 0.0;
        cv::minMaxLoc(mosaic, &minv, &maxv);
        double scale = (maxv > minv) ? 255.0 / (maxv - minv) : 1.0;
        mosaic.convertTo(mosaic8, CV_8U, scale, -minv * scale);
    }
    auto det2 = pImpl->detector->Detect(mosaic8);
    std::vector<Detection> out;
    out.reserve(det2.size());
    for (const auto& d : det2)
    {
        int cx = d.box.x + d.box.width / 2;
        int cy = d.box.y + d.box.height / 2;
        int gx = std::min(grid_n - 1, std::max(0, (cx) / gw));
        int gy = std::min(grid_n - 1, std::max(0, (cy) / gh));
        int zi = grid_z.at<int>(gy, gx);
        Detection dd = d;
        dd.box.x += rx0;
        dd.box.y += ry0;
        dd.z = zi;
        out.push_back(dd);
    }
    return out.empty() ? detections : out;
}

// Refine center and diameter using 2D segmentation (aligned to Python _refine_center_and_diameter)
std::tuple<int, int, int> SphereSeg3D::refineCenterAndDiameter(const VolumeData& volume, const Detection& detection, int targetLayer)
{
    int W = volume.width;
    int H = volume.height;

    // Initial values from detection
    int cx0 = detection.box.x + detection.box.width / 2;
    int cy0 = detection.box.y + detection.box.height / 2;
    int diam0 = std::max(detection.box.width, detection.box.height);

    // Extract ROI bounds
    int x1 = std::max(0, detection.box.x);
    int y1 = std::max(0, detection.box.y);
    int x2 = std::min(W, detection.box.x + detection.box.width);
    int y2 = std::min(H, detection.box.y + detection.box.height);

    if (x2 <= x1 || y2 <= y1)
    {
        return std::make_tuple(cx0, cy0, diam0);
    }

    // Get slice at target layer
    size_t slice_voxels = static_cast<size_t>(W) * H;
    size_t offset = static_cast<size_t>(targetLayer) * slice_voxels;

    cv::Mat slice;
    if (volume.is_signed)
    {
        const int16_t* ptr = reinterpret_cast<const int16_t*>(volume.data.get()) + offset;
        slice = cv::Mat(H, W, CV_16SC1, const_cast<int16_t*>(ptr));
    }
    else
    {
        const uint16_t* ptr = reinterpret_cast<const uint16_t*>(volume.data.get()) + offset;
        slice = cv::Mat(H, W, CV_16UC1, const_cast<uint16_t*>(ptr));
    }

    // Extract ROI
    cv::Mat roi = slice(cv::Rect(x1, y1, x2 - x1, y2 - y1));

    // Auto threshold using Otsu
    double minVal, maxVal;
    cv::minMaxLoc(roi, &minVal, &maxVal);

    if (maxVal <= minVal)
    {
        return std::make_tuple(cx0, cy0, diam0);
    }

    // Convert to 8-bit for thresholding
    cv::Mat roi8;
    double scale = 255.0 / (maxVal - minVal);
    roi.convertTo(roi8, CV_8U, scale, -minVal * scale);

    // Apply Otsu threshold
    double otsu_thresh = cv::threshold(roi8, roi8, 0, 255, cv::THRESH_BINARY | cv::THRESH_OTSU);

    // Find contours
    std::vector<std::vector<cv::Point>> contours;
    cv::findContours(roi8, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);

    if (contours.empty())
    {
        return std::make_tuple(cx0, cy0, diam0);
    }

    // Find largest contour
    double maxArea = 0;
    int maxAreaIdx = 0;
    for (size_t i = 0; i < contours.size(); ++i)
    {
        double area = cv::contourArea(contours[i]);
        if (area > maxArea)
        {
            maxArea = area;
            maxAreaIdx = static_cast<int>(i);
        }
    }

    // Get bounding rect of largest contour
    cv::Rect boundRect = cv::boundingRect(contours[maxAreaIdx]);

    // Refined center and diameter (convert back to global coordinates)
    int rcx = x1 + boundRect.x + boundRect.width / 2;
    int rcy = y1 + boundRect.y + boundRect.height / 2;
    int rdiam = std::max(boundRect.width, boundRect.height);

    // Ensure refined values are reasonable
    rcx = std::max(0, std::min(W - 1, rcx));
    rcy = std::max(0, std::min(H - 1, rcy));
    rdiam = std::max(1, std::min(std::min(W, H), rdiam));

    return std::make_tuple(rcx, rcy, rdiam);
}

// Segment spheres with class-specific constraints (aligned to Python)
// Perform binary segmentation for detected spheres
// Parameters:
//   volume: 3D volume data
//   detections: List of detected objects with bounding boxes
//   targetLayer: Reference layer for segmentation (unused in current implementation)
// Returns: Vector of binary masks, one per detection
std::vector<std::vector<bool>> SphereSeg3D::segmentSpheresBinary(const VolumeData& volume, const std::vector<Detection>& detections, int targetLayer)
{
    std::vector<std::vector<bool>> sphere_masks;
    if (detections.empty())
    {
        return sphere_masks;
    }

    int Z = volume.depth, H = volume.height, W = volume.width;
    size_t voxels = static_cast<size_t>(W) * H * Z;

    // Compute average side length for class-0 boxes (used for class-1 Z thickness)
    int sum_side0 = 0, cnt_side0 = 0;
    for (const auto& d : detections)
    {
        if (d.classId == 0)
        {
            int s0 = std::max(d.box.width, d.box.height);
            if (s0 > 0) { sum_side0 += s0; ++cnt_side0; }
        }
    }
    int avg_side0 = (cnt_side0 > 0) ? std::max(1, sum_side0 / cnt_side0) : -1;

    auto voxel_value = [&](int z, int y, int x) -> double
    {
        size_t idx = static_cast<size_t>(z) * W * H + static_cast<size_t>(y) * W + x;
        if (volume.is_signed)
        {
            return static_cast<double>(reinterpret_cast<const int16_t*>(volume.data.get())[idx]);
        }
        else
        {
            return static_cast<double>(reinterpret_cast<const uint16_t*>(volume.data.get())[idx]);
        }
    };

    // Adjustable threshold parameters (aligned to Python)
    const double THRESHOLD_BIAS = 1.05;  // Slightly higher threshold to reduce segmentation volume
    const int MIN_REGION_SIZE = 32;      // Small region threshold

    auto auto_threshold = [&](int z0, int z1, int y0, int y1, int x0, int x1) -> double
    {
        // Otsu-ish fallback: mean for small, else Otsu; if Otsu fails, mean+0.5*std
        int dz = std::max(0, z1 - z0); int dy = std::max(0, y1 - y0); int dx = std::max(0, x1 - x0);
        size_t n = static_cast<size_t>(dz) * dy * dx;
        if (n < MIN_REGION_SIZE) // small region
        {
            double sum = 0.0; size_t cnt = 0;
            for (int z = z0; z < z1; ++z) for (int y = y0; y < y1; ++y) for (int x = x0; x < x1; ++x) { sum += voxel_value(z,y,x); ++cnt; }
            return cnt ? (sum / cnt * THRESHOLD_BIAS) : 0.0;  // Apply bias to small regions
        }
        // Compute histogram (256 bins) by linear normalization
        double vmin = 0.0, vmax = 0.0;
        bool init = false;
        for (int z = z0; z < z1; ++z)
        {
            for (int y = y0; y < y1; ++y)
            {
                for (int x = x0; x < x1; ++x)
                {
                    double v = voxel_value(z,y,x);
                    if (!init) { vmin = vmax = v; init = true; }
                    else { vmin = std::min(vmin, v); vmax = std::max(vmax, v); }
                }
            }
        }
        if (!init || vmin >= vmax)
        {
            return vmin;
        }
        std::vector<int> hist(256, 0);
        auto to_bin = [&](double v) -> int
        {
            int b = static_cast<int>(std::floor((v - vmin) * 255.0 / (vmax - vmin) + 0.5));
            return std::max(0, std::min(255, b));
        };
        for (int z = z0; z < z1; ++z)
        {
            for (int y = y0; y < y1; ++y)
            {
                for (int x = x0; x < x1; ++x)
                {
                    hist[to_bin(voxel_value(z,y,x))]++;
                }
            }
        }
        // Otsu
        std::vector<double> prob(256, 0.0);
        double total = 0.0; for (int i = 0; i < 256; ++i) total += hist[i];
        if (total <= 0.0)
        {
            return (vmin + vmax) * 0.5;
        }
        for (int i = 0; i < 256; ++i) prob[i] = hist[i] / total;
        std::vector<double> omega(256, 0.0), mu(256, 0.0);
        omega[0] = prob[0]; mu[0] = 0.0 * prob[0];
        for (int i = 1; i < 256; ++i)
        {
            omega[i] = omega[i - 1] + prob[i];
            mu[i] = mu[i - 1] + i * prob[i];
        }
        double mu_t = mu[255];
        double max_sigma = -1.0; int k_star = 0;
        for (int k = 0; k < 256; ++k)
        {
            if (omega[k] <= 0.0 || omega[k] >= 1.0) continue;
            double mu0 = mu[k] / omega[k];
            double mu1 = (mu_t - mu[k]) / (1.0 - omega[k]);
            double sigma_b = omega[k] * (1.0 - omega[k]) * (mu0 - mu1) * (mu0 - mu1);
            if (sigma_b > max_sigma) { max_sigma = sigma_b; k_star = k; }
        }
        double th_norm = static_cast<double>(k_star) / 255.0;
        double otsu_th = vmin + th_norm * (vmax - vmin);
        return otsu_th * THRESHOLD_BIAS;  // Apply bias to Otsu threshold
    };

    for (const auto& det : detections)
    {
        if (det.classId == 3)
        {
            continue; // skip class-3
        }
        int x1 = std::max(0, det.box.x);
        int y1 = std::max(0, det.box.y);
        int x2 = std::min(W, det.box.x + det.box.width);
        int y2 = std::min(H, det.box.y + det.box.height);
        int side = std::max(x2 - x1, y2 - y1);

        std::vector<bool> mask(voxels, false);

        int cz = det.z; // Use individual z from detection instead of target_layer

        if (det.classId == 1)
        {
            // Class 1: XY=box, Z=avg_side (centered at cz)
            int diam_z = std::max(1, avg_side0);
            int z0 = std::max(0, static_cast<int>(cz - std::floor(diam_z / 2.0)));
            int z1 = std::min(Z, z0 + diam_z);
            double th = auto_threshold(z0, z1, y1, y2, x1, x2);

            // Debug: Print threshold for class 1
            std::cout << "Class 1 sphere: box(" << x1 << "," << y1 << "," << x2 << "," << y2
                      << "), z[" << z0 << ":" << z1 << "], threshold=" << th << std::endl;

            for (int z = z0; z < z1; ++z)
            {
                for (int y = y1; y < y2; ++y)
                {
                    for (int x = x1; x < x2; ++x)
                    {
                        size_t idx = static_cast<size_t>(z) * W * H + static_cast<size_t>(y) * W + x;
                        if (voxel_value(z, y, x) >= th)
                        {
                            mask[idx] = true;
                        }
                    }
                }
            }
        }
        else
        {
            // Class 0/2: Use refined center and diameter (align to Python _refine_center_and_diameter)
            auto refined_result = refineCenterAndDiameter(volume, det, cz);
            int rcx = std::get<0>(refined_result);
            int rcy = std::get<1>(refined_result);
            int rdiam = std::get<2>(refined_result);
            double r = rdiam / 2.0;

            int z0 = std::max(0, static_cast<int>(cz - std::floor(r)));
            int z1 = std::min(Z, z0 + rdiam);
            int y0 = std::max(0, static_cast<int>(rcy - std::floor(r)));
            int y1r = std::min(H, y0 + rdiam);
            int x0 = std::max(0, static_cast<int>(rcx - std::floor(r)));
            int x1r = std::min(W, x0 + rdiam);

            // Collect sphere values for threshold (align to Python)
            std::vector<double> sphere_vals;
            for (int z = z0; z < z1; ++z)
            {
                for (int y = y0; y < y1r; ++y)
                {
                    for (int x = x0; x < x1r; ++x)
                    {
                        double dz = (z - cz);
                        double dy = (y - rcy);
                        double dx = (x - rcx);
                        if (dz * dz + dy * dy + dx * dx <= r * r)
                        {
                            sphere_vals.push_back(voxel_value(z, y, x));
                        }
                    }
                }
            }

            double th = sphere_vals.empty() ? 0.0 :
                (sphere_vals.size() < 32 ?
                 std::accumulate(sphere_vals.begin(), sphere_vals.end(), 0.0) / sphere_vals.size() :
                 auto_threshold(z0, z1, y0, y1r, x0, x1r));

            for (int z = z0; z < z1; ++z)
            {
                for (int y = y0; y < y1r; ++y)
                {
                    for (int x = x0; x < x1r; ++x)
                    {
                        double dz = (z - cz);
                        double dy = (y - rcy);
                        double dx = (x - rcx);
                        if (dz * dz + dy * dy + dx * dx <= r * r)
                        {
                            size_t idx = static_cast<size_t>(z) * W * H + static_cast<size_t>(y) * W + x;
                            if (voxel_value(z, y, x) >= th)
                            {
                                mask[idx] = true;
                            }
                        }
                    }
                }
            }
        }

        sphere_masks.push_back(std::move(mask));
    }

    return sphere_masks;
}

// Save 3D segmentation result with random background (aligned to Python behavior)
bool SphereSeg3D::generateSegmentationResult(const VolumeData& volume, const std::vector<std::vector<bool>>& sphereMasks,
                                             int targetLayer, const std::string& outputPath, SegmentationResult& result)
{
    if (sphereMasks.empty())
    {
        std::cout << "No sphere masks" << std::endl;
        return false;
    }

    namespace fs = std::filesystem;
    if (!outputPath.empty())
    {
        fs::create_directories(outputPath);
    }

    int Z = volume.depth, H = volume.height, W = volume.width;
    size_t voxels = static_cast<size_t>(W) * H * Z;

    std::cout << "Generating 3D segmentation..." << std::endl;
    std::cout << "Input dims: " << Z << " x " << H << " x " << W << std::endl;
    std::cout << "Input dtype: " << (volume.is_signed ? "int16" : "uint16") << std::endl;
    std::cout << "Spheres: " << sphereMasks.size() << std::endl;

    // Debug: Check if we have any valid masks
    size_t totalTrueMasks = 0;
    for (const auto& mask : sphereMasks)
    {
        for (bool val : mask)
        {
            if (val) totalTrueMasks++;
        }
    }
    std::cout << "Total true mask pixels: " << totalTrueMasks << std::endl;

    // Step 2: Merge all sphere masks (aligned to Python: combined_mask |= mask)
    std::vector<bool> combined_mask(voxels, false);
    for (const auto& mask : sphereMasks)
    {
        for (size_t i = 0; i < voxels && i < mask.size(); ++i)
        {
            if (mask[i])
            {
                combined_mask[i] = true;
            }
        }
    }

    // Step 3: 3D connected component labeling (aligned to Python)
    std::cout << "Performing 3D connected component analysis..." << std::endl;
    std::vector<int> labeled_mask(voxels, 0);
    int num_features = 0;

    // Simple connected component labeling using flood fill
    auto idx3 = [&](int z, int y, int x) -> size_t { return static_cast<size_t>(z) * W * H + static_cast<size_t>(y) * W + x; };
    auto inBounds = [&](int z, int y, int x) -> bool { return z >= 0 && z < Z && y >= 0 && y < H && x >= 0 && x < W; };

    // 3D 26-connectivity offsets
    std::vector<std::tuple<int, int, int>> offsets;
    for (int dz = -1; dz <= 1; ++dz)
        for (int dy = -1; dy <= 1; ++dy)
            for (int dx = -1; dx <= 1; ++dx)
                if (dz != 0 || dy != 0 || dx != 0)
                    offsets.push_back({dz, dy, dx});

    for (int z = 0; z < Z; ++z)
    {
        for (int y = 0; y < H; ++y)
        {
            for (int x = 0; x < W; ++x)
            {
                size_t idx = idx3(z, y, x);
                if (combined_mask[idx] && labeled_mask[idx] == 0)
                {
                    // Start new component
                    ++num_features;
                    std::queue<std::tuple<int, int, int>> queue;
                    queue.push({z, y, x});
                    labeled_mask[idx] = num_features;

                    while (!queue.empty())
                    {
                        auto [cz, cy, cx] = queue.front();
                        queue.pop();

                        for (auto [dz, dy, dx] : offsets)
                        {
                            int nz = cz + dz, ny = cy + dy, nx = cx + dx;
                            if (inBounds(nz, ny, nx))
                            {
                                size_t nidx = idx3(nz, ny, nx);
                                if (combined_mask[nidx] && labeled_mask[nidx] == 0)
                                {
                                    labeled_mask[nidx] = num_features;
                                    queue.push({nz, ny, nx});
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    std::cout << "Connected components: " << num_features << std::endl;

    // Step 4: Calculate maximum value for each connected component (aligned to Python)
    std::vector<double> comp_max(num_features + 1, 0.0);
    if (num_features > 0)
    {
        if (volume.is_signed)
        {
            const int16_t* src = reinterpret_cast<const int16_t*>(volume.data.get());
            for (size_t i = 0; i < voxels; ++i)
            {
                if (labeled_mask[i] > 0)
                {
                    int label = labeled_mask[i];
                    comp_max[label] = std::max(comp_max[label], static_cast<double>(src[i]));
                }
            }
        }
        else
        {
            const uint16_t* src = reinterpret_cast<const uint16_t*>(volume.data.get());
            for (size_t i = 0; i < voxels; ++i)
            {
                if (labeled_mask[i] > 0)
                {
                    int label = labeled_mask[i];
                    comp_max[label] = std::max(comp_max[label], static_cast<double>(src[i]));
                }
            }
        }
    }

    // Step 5: Binary dilation (aligned to Python: ndimage.binary_dilation)
    std::cout << "Performing binary dilation..." << std::endl;
    std::vector<bool> dilated_combined_mask(voxels, false);
    std::vector<int> dilated_labels(voxels, 0);

    // Copy original mask and labels
    for (size_t i = 0; i < voxels; ++i)
    {
        dilated_combined_mask[i] = combined_mask[i];
        dilated_labels[i] = labeled_mask[i];
    }

    // Single iteration dilation with 26-connectivity
    for (int z = 0; z < Z; ++z)
    {
        for (int y = 0; y < H; ++y)
        {
            for (int x = 0; x < W; ++x)
            {
                size_t idx = idx3(z, y, x);
                if (combined_mask[idx])
                {
                    // Dilate to neighbors
                    for (auto [dz, dy, dx] : offsets)
                    {
                        int nz = z + dz, ny = y + dy, nx = x + dx;
                        if (inBounds(nz, ny, nx))
                        {
                            size_t nidx = idx3(nz, ny, nx);
                            dilated_combined_mask[nidx] = true;
                            if (dilated_labels[nidx] == 0)
                            {
                                dilated_labels[nidx] = labeled_mask[idx];
                            }
                        }
                    }
                }
            }
        }
    }

    // Hole filling processing (aligned to Python binary_fill_holes)
    std::cout << "Processing holes in 2D slices..." << std::endl;
    std::vector<bool> hole_mask(voxels, false);

    for (int z = 0; z < Z; ++z)
    {
        // Extract 2D slice from combined mask
        std::vector<bool> slice(W * H);
        for (int y = 0; y < H; ++y)
        {
            for (int x = 0; x < W; ++x)
            {
                size_t idx_3d = static_cast<size_t>(z) * W * H + static_cast<size_t>(y) * W + x;
                size_t idx_2d = static_cast<size_t>(y) * W + x;
                slice[idx_2d] = combined_mask[idx_3d];
            }
        }

        // Check if slice has any foreground pixels
        bool hasAny = false;
        for (bool pixel : slice)
        {
            if (pixel)
            {
                hasAny = true;
                break;
            }
        }

        if (hasAny)
        {
            // Fill holes in this slice
            std::vector<bool> filled = binaryFillHoles2D(slice, W, H);

            // Identify holes: filled & (~original)
            for (int y = 0; y < H; ++y)
            {
                for (int x = 0; x < W; ++x)
                {
                    size_t idx_3d = static_cast<size_t>(z) * W * H + static_cast<size_t>(y) * W + x;
                    size_t idx_2d = static_cast<size_t>(y) * W + x;

                    if (filled[idx_2d] && !slice[idx_2d])
                    {
                        hole_mask[idx_3d] = true;
                    }
                }
            }
        }
    }

    // Step 7: Generate corrected volume with max_value_scale (aligned to Python)
    std::cout << "Generating corrected volume..." << std::endl;
    const double max_value_scale = 0.5;  // Aligned to Python

    // Create corrected volume and segmented volume
    size_t bytesPerVoxel = volume.is_signed ? sizeof(int16_t) : sizeof(uint16_t);
    result.width = W; result.height = H; result.depth = Z; result.is_signed = volume.is_signed; result.crop_x0 = 0; result.crop_y0 = 0;
    result.data = std::make_unique<uint8_t[]>(voxels * bytesPerVoxel);

    std::vector<double> correct_volume(voxels, 0.0);
    std::vector<double> segmented_volume(voxels, 0.0);

    // Step 8: Generate corrected values in dilated region (aligned to Python)
    if (volume.is_signed)
    {
        const int16_t* src = reinterpret_cast<const int16_t*>(volume.data.get());

        for (size_t i = 0; i < voxels; ++i)
        {
            if (dilated_combined_mask[i] && num_features > 0)
            {
                int label = dilated_labels[i];
                if (label > 0 && label <= num_features)
                {
                    double original_val = static_cast<double>(src[i]);
                    double max_val = comp_max[label];
                    // Python: corr_valid = (1 - max_value_scale) * vals[valid] + max_value_scale * max_vals
                    correct_volume[i] = (1.0 - max_value_scale) * original_val + max_value_scale * max_val;
                }
            }
        }
    }
    else
    {
        const uint16_t* src = reinterpret_cast<const uint16_t*>(volume.data.get());

        for (size_t i = 0; i < voxels; ++i)
        {
            if (dilated_combined_mask[i] && num_features > 0)
            {
                int label = dilated_labels[i];
                if (label > 0 && label <= num_features)
                {
                    double original_val = static_cast<double>(src[i]);
                    double max_val = comp_max[label];
                    correct_volume[i] = (1.0 - max_value_scale) * original_val + max_value_scale * max_val;
                }
            }
        }
    }

    // Step 9: Compose final segmented volume (aligned to Python)
    if (volume.is_signed)
    {
        const int16_t* src = reinterpret_cast<const int16_t*>(volume.data.get());
        int16_t* dst = reinterpret_cast<int16_t*>(result.data.get());

        // Copy corrected values to dilated regions
        for (size_t i = 0; i < voxels; ++i)
        {
            if (dilated_combined_mask[i])
            {
                segmented_volume[i] = correct_volume[i];
            }
        }

        // Generate random background values (aligned to Python)
        std::vector<int16_t> bg_values;
        bg_values.reserve(voxels);
        for (size_t i = 0; i < voxels; ++i)
        {
            if (!dilated_combined_mask[i])
            {
                bg_values.push_back(src[i]);
            }
        }

        if (!bg_values.empty())
        {
            std::random_device rd;
            std::mt19937 rng(rd());
            std::uniform_int_distribution<size_t> dist(0, bg_values.size() - 1);

            for (size_t i = 0; i < voxels; ++i)
            {
                if (!dilated_combined_mask[i])
                {
                    int16_t random_bg = bg_values[dist(rng)];
                    segmented_volume[i] = static_cast<double>(random_bg) * 0.7;
                }
            }
        }

        // Keep holes as 0 (aligned to Python: segmented_volume[hole_mask] = 0)
        for (size_t i = 0; i < voxels; ++i)
        {
            if (hole_mask[i])
            {
                segmented_volume[i] = 0.0;
            }
        }

        // Convert to final output
        for (size_t i = 0; i < voxels; ++i)
        {
            dst[i] = static_cast<int16_t>(std::round(segmented_volume[i]));
        }
    }
    else
    {
        const uint16_t* src = reinterpret_cast<const uint16_t*>(volume.data.get());
        uint16_t* dst = reinterpret_cast<uint16_t*>(result.data.get());

        // Copy corrected values to dilated regions
        for (size_t i = 0; i < voxels; ++i)
        {
            if (dilated_combined_mask[i])
            {
                segmented_volume[i] = correct_volume[i];
            }
        }

        // Generate random background values (aligned to Python)
        std::vector<uint16_t> bg_values;
        bg_values.reserve(voxels);
        for (size_t i = 0; i < voxels; ++i)
        {
            if (!dilated_combined_mask[i])
            {
                bg_values.push_back(src[i]);
            }
        }

        if (!bg_values.empty())
        {
            std::random_device rd;
            std::mt19937 rng(rd());
            std::uniform_int_distribution<size_t> dist(0, bg_values.size() - 1);

            for (size_t i = 0; i < voxels; ++i)
            {
                if (!dilated_combined_mask[i])
                {
                    uint16_t random_bg = bg_values[dist(rng)];
                    segmented_volume[i] = static_cast<double>(random_bg) * 0.7;
                }
            }
        }

        // Keep holes as 0 (aligned to Python: segmented_volume[hole_mask] = 0)
        for (size_t i = 0; i < voxels; ++i)
        {
            if (hole_mask[i])
            {
                segmented_volume[i] = 0.0;
            }
        }

        // Convert to final output
        for (size_t i = 0; i < voxels; ++i)
        {
            dst[i] = static_cast<uint16_t>(std::round(segmented_volume[i]));
        }
    }

    // DLL only processes data, file saving is handled by the application

    return true;
}

// Binary hole filling for 2D slice (similar to scipy.ndimage.binary_fill_holes)
// Parameters:
//   slice: 2D binary mask
//   width, height: Slice dimensions
// Returns: Filled binary mask
std::vector<bool> SphereSeg3D::binaryFillHoles2D(const std::vector<bool>& slice, int width, int height)
{
    ss3d::Segmenter seg;
    return seg.binaryFillHoles2D(slice, width, height);
}
