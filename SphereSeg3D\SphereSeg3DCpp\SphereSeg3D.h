#pragma once

#include <vector>
#include <string>
#include <cstdint>
#include <memory>
#include <optional>

#ifdef SPHERESEG3D_EXPORTS
#define SPHERESEG3D_API __declspec(dllexport)
#else
#define SPHERESEG3D_API __declspec(dllimport)
#endif

// Forward declarations to avoid conflicts
struct Detection;

// Modern volume data structure with RAII
struct VolumeData
{
    std::unique_ptr<uint8_t[]> data;  // Raw volume data managed by smart pointer
    int width;
    int height;
    int depth;
    bool is_signed;      // true for int16, false for uint16

    VolumeData()
        : data(nullptr)
        , width(0)
        , height(0)
        , depth(0)
        , is_signed(true)
    {
    }

    // Move constructor and assignment
    VolumeData(VolumeData&& other) noexcept
        : data(std::move(other.data))
        , width(other.width)
        , height(other.height)
        , depth(other.depth)
        , is_signed(other.is_signed)
    {
        other.width = other.height = other.depth = 0;
    }

    VolumeData& operator=(VolumeData&& other) noexcept
    {
        if (this != &other)
        {
            data = std::move(other.data);
            width = other.width;
            height = other.height;
            depth = other.depth;
            is_signed = other.is_signed;
            other.width = other.height = other.depth = 0;
        }
        return *this;
    }

    // Delete copy constructor and assignment
    VolumeData(const VolumeData&) = delete;
    VolumeData& operator=(const VolumeData&) = delete;

    // Get total voxel count
    size_t getTotalVoxels() const noexcept
    {
        return static_cast<size_t>(width) * height * depth;
    }

    // Get bytes per voxel
    size_t getBytesPerVoxel() const noexcept
    {
        return is_signed ? sizeof(int16_t) : sizeof(uint16_t);
    }

    // Get total data size in bytes
    size_t getTotalBytes() const noexcept
    {
        return getTotalVoxels() * getBytesPerVoxel();
    }
};

// Modern segmentation result with RAII
struct SegmentationResult
{
    std::unique_ptr<uint8_t[]> data;  // Segmented volume data managed by smart pointer
    int width, height, depth;
    bool is_signed;
    int crop_x0, crop_y0; // Crop offset

    SegmentationResult()
        : data(nullptr)
        , width(0)
        , height(0)
        , depth(0)
        , is_signed(true)
        , crop_x0(0)
        , crop_y0(0)
    {
    }

    // Move constructor and assignment
    SegmentationResult(SegmentationResult&& other) noexcept
        : data(std::move(other.data))
        , width(other.width)
        , height(other.height)
        , depth(other.depth)
        , is_signed(other.is_signed)
        , crop_x0(other.crop_x0)
        , crop_y0(other.crop_y0)
    {
        other.width = other.height = other.depth = 0;
        other.crop_x0 = other.crop_y0 = 0;
    }

    SegmentationResult& operator=(SegmentationResult&& other) noexcept
    {
        if (this != &other)
        {
            data = std::move(other.data);
            width = other.width;
            height = other.height;
            depth = other.depth;
            is_signed = other.is_signed;
            crop_x0 = other.crop_x0;
            crop_y0 = other.crop_y0;
            other.width = other.height = other.depth = 0;
            other.crop_x0 = other.crop_y0 = 0;
        }
        return *this;
    }

    // Delete copy constructor and assignment
    SegmentationResult(const SegmentationResult&) = delete;
    SegmentationResult& operator=(const SegmentationResult&) = delete;

    // Get total voxel count
    size_t getTotalVoxels() const noexcept
    {
        return static_cast<size_t>(width) * height * depth;
    }

    // Get bytes per voxel
    size_t getBytesPerVoxel() const noexcept
    {
        return is_signed ? sizeof(int16_t) : sizeof(uint16_t);
    }

    // Get total data size in bytes
    size_t getTotalBytes() const noexcept
    {
        return getTotalVoxels() * getBytesPerVoxel();
    }
};

// C ABI friendly result struct (no destructor)
extern "C" {
struct SS3DResult
{
    void* data;
    int width, height, depth;
    int is_signed; // 1 for int16, 0 for uint16
    int crop_x0, crop_y0;
};
}

// Processing configuration with modern defaults
struct ProcessingConfig
{
    std::string modelPath;           // Path to ONNX model file
    bool useCuda = false;           // Enable CUDA acceleration
    int numThreads = 1;             // Number of processing threads
    bool enableVerboseLogging = false;  // Enable detailed logging

    // Processing parameters
    double thresholdBias = 1.2;     // Threshold bias coefficient (>1.0 increases threshold)
    int minRegionSize = 32;         // Minimum region size for processing

    ProcessingConfig() = default;

    explicit ProcessingConfig(const std::string& model)
        : modelPath(model)
    {
    }
};

// Main sphere segmentation processor with modern C++20 interface
class SPHERESEG3D_API SphereSeg3D
{
public:
    SphereSeg3D();
    ~SphereSeg3D();

    // Primary processing interface - process volume data from memory
    bool processVolumeFromMemory(const void* volumeData,
                                int width,
                                int height,
                                int depth,
                                bool isSigned,
                                const ProcessingConfig& config,
                                SegmentationResult& result);

    // Convenience method - process volume from file directory
    bool processVolumeFromDirectory(const std::string& inputPath,
                                   const std::string& outputPath,
                                   const ProcessingConfig& config);

    // Load volume data from directory into memory
    std::optional<VolumeData> loadVolumeFromDirectory(const std::string& inputPath);

    // Individual processing steps (for advanced users)
    int computeAutoFocus(const VolumeData& volume);
    std::vector<int> AMPD(const std::vector<double>& data);
    std::vector<Detection> detectObjects(const VolumeData& volume, int targetLayer, const std::string& modelPath);
    std::vector<bool> binaryFillHoles2D(const std::vector<bool>& slice, int width, int height);
    std::vector<std::vector<Detection>> detectObjectsBatch(const VolumeData& volume, const std::vector<int>& targetLayers, const std::string& modelPath);
    std::vector<Detection> filterByClass3BigBox(const std::vector<Detection>& detections, int width, int height);
    std::vector<Detection> applyTiltCorrection(const VolumeData& volume, std::vector<Detection>& detections);
    std::vector<std::vector<bool>> segmentSpheresBinary(const VolumeData& volume, const std::vector<Detection>& detections, int targetLayer);
    std::tuple<int, int, int> refineCenterAndDiameter(const VolumeData& volume, const Detection& detection, int targetLayer);
    bool generateSegmentationResult(const VolumeData& volume, const std::vector<std::vector<bool>>& sphereMasks,
                                   int targetLayer, const std::string& outputPath, SegmentationResult& result);

    // Legacy functions (for compatibility)
    bool load_volume_from_slices(const std::string& input_path, VolumeData& volume);
    bool process_volume(const std::string& input_path, const std::string& output_path, const std::string& model_path = "");

    // Performance timing information
    struct TimingInfo
    {
        double dataLoadTime = 0.0;
        double focusTime = 0.0;
        double aiInitTime = 0.0;
        double aiInferTime = 0.0;
        double segmentationTime = 0.0;
        double saveTime = 0.0;
        double totalTime = 0.0;

        void reset() noexcept
        {
            dataLoadTime = focusTime = aiInitTime = aiInferTime = 0.0;
            segmentationTime = saveTime = totalTime = 0.0;
        }
    } timing;

private:
    class Impl;
    Impl* pImpl;
};

// -------- C ABI wrapper (stable across compilers/languages) --------
extern "C" {
    typedef void* SS3DHandle;
    SPHERESEG3D_API SS3DHandle ss3d_create(const char* model_path);
    SPHERESEG3D_API void ss3d_destroy(SS3DHandle h);
    SPHERESEG3D_API int ss3d_process_memory(SS3DHandle h,
                                            const void* data,
                                            int width,
                                            int height,
                                            int depth,
                                            int is_signed,
                                            SS3DResult* out);
    SPHERESEG3D_API void ss3d_free_result(SS3DResult* out);
}
