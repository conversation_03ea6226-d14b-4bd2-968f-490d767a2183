<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{5E3F6E7F-4C5B-4E65-9A64-AB3B40B5B9A1}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>SphereSeg3D</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ProjectName>SphereSeg3D</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <LanguageStandard>stdcpp20</LanguageStandard>
    <WholeProgramOptimization>false</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <LanguageStandard>stdcpp20</LanguageStandard>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings" />
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <VcpkgApplocalDeps>false</VcpkgApplocalDeps>
    <VcpkgUsePowerShell>true</VcpkgUsePowerShell>
    <VcpkgUsePowerShellCore>false</VcpkgUsePowerShellCore>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <OutDir>$(SolutionDir)x64\$(Configuration)\</OutDir>
    <IntDir>$(ProjectDir)x64\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <OutDir>$(SolutionDir)x64\$(Configuration)\</OutDir>
    <IntDir>$(ProjectDir)x64\$(Configuration)\</IntDir>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);</IncludePath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <PreprocessorDefinitions>SPHERESEG3D_EXPORTS;_CRT_SECURE_NO_WARNINGS;ENABLE_WELTDETECT_ONNX=1;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <TreatWarningAsError>false</TreatWarningAsError>
      <AdditionalIncludeDirectories>$(ProjectDir);$(ThirdPartyLibraries)\opencv-4.12.0\opencv\build\include;$(ThirdPartyLibraries)\onnxruntime-win-x64-gpu-1.22.1\include</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>opencv_world4120d.lib;onnxruntime.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(ThirdPartyLibraries)\opencv-4.12.0\opencv\build\x64\vc16\lib;$(ThirdPartyLibraries)\onnxruntime-win-x64-gpu-1.22.1\lib</AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
      <PreprocessorDefinitions>SPHERESEG3D_EXPORTS;_CRT_SECURE_NO_WARNINGS;NDEBUG;ENABLE_WELTDETECT_ONNX=1;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <TreatWarningAsError>false</TreatWarningAsError>
      <AdditionalIncludeDirectories>$(ProjectDir);$(ThirdPartyLibraries)\opencv-4.12.0\opencv\build\include;$(ThirdPartyLibraries)\onnxruntime-win-x64-gpu-1.22.1\include;</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>opencv_world4120.lib;onnxruntime.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(ThirdPartyLibraries)\opencv-4.12.0\opencv\build\x64\vc16\lib;$(ThirdPartyLibraries)\onnxruntime-win-x64-gpu-1.22.1\lib</AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="Detector.h" />
    <ClInclude Include="SphereSeg3D.h" />
    <ClInclude Include="SphereSeg3D_Public.h" />
    <ClInclude Include="FocusAnalyzer.h" />
    <ClInclude Include="Segmenter.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="SphereSeg3D.cpp" />
    <ClCompile Include="Detector.cpp" />
    <ClCompile Include="SphereSeg3D_Public.cpp" />
    <ClCompile Include="FocusAnalyzer.cpp" />
    <ClCompile Include="Segmenter.cpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets" />
</Project>