// Windows(CR LF), UTF-8
#pragma once
#include <stdint.h>
#ifdef _WIN32
  #ifdef SPHERESEG3D_EXPORTS
    #define SS3D_API __declspec(dllexport)
  #else
    #define SS3D_API __declspec(dllimport)
  #endif
#else
  #define SS3D_API
#endif
#ifdef __cplusplus
extern "C" {
#endif

typedef void* SS3DHandle;

typedef struct SS3DResult {
    void* data;
    int width, height, depth;
    int is_signed; // 1=int16, 0=uint16
    int crop_x0, crop_y0;
} SS3DResult;

// Original functions (kept)
SS3D_API SS3DHandle ss3d_create(const char* model_path);
SS3D_API void ss3d_destroy(SS3DHandle h);
SS3D_API int ss3d_process_memory(SS3DHandle h,
                                 const void* data,
                                 int width,
                                 int height,
                                 int depth,
                                 int is_signed,
                                 SS3DResult* out);
SS3D_API void ss3d_free_result(SS3DResult* out);

// Descriptive alias names for clarity
SS3D_API SS3DHandle ss3dCreateWithModel(const char* model_path);
SS3D_API int ss3dProcessVolumeFromMemory(SS3DHandle h,
                                         const void* data,
                                         int width,
                                         int height,
                                         int depth,
                                         int is_signed,
                                         SS3DResult* out);
SS3D_API void ss3dFreeResult(SS3DResult* out);
SS3D_API void ss3dDestroy(SS3DHandle h);

#ifdef __cplusplus
}
#endif

