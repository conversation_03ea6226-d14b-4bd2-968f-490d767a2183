#include "SphereSeg3D_Public.h"
#include "SphereSeg3D.h"
#include <string>
#include <memory>
#include <filesystem>
#include <fstream>
#include <iostream>

// Forward declaration of internal class
class SphereSeg3DInternal;

// Implementation class to hide all internal details
class SphereSeg3DProcessor_Impl
{
public:
    std::unique_ptr<SphereSeg3DInternal> processor;
    std::string lastError;
    
    void setError(const std::string& error)
    {
        lastError = error;
    }
    
    void clearError()
    {
        lastError.clear();
    }
};

#include "SphereSeg3D.h"

// Internal wrapper class
class SphereSeg3DInternal
{
public:
    SphereSeg3D processor;
};

// Constructor
SphereSeg3DProcessor::SphereSeg3DProcessor()
    : pImpl(new SphereSeg3DProcessor_Impl())
{
    pImpl->processor = std::make_unique<SphereSeg3DInternal>();
}

// Destructor
SphereSeg3DProcessor::~SphereSeg3DProcessor()
{
    delete pImpl;
}

// Primary interface - process volume data from memory
bool SphereSeg3DProcessor::processVolumeFromMemory(const void* volumeData,
                                                   int width,
                                                   int height,
                                                   int depth,
                                                   bool isSigned,
                                                   const ProcessingOptions& options,
                                                   ProcessedVolume& result)
{
    if (!volumeData || width <= 0 || height <= 0 || depth <= 0 || !options.modelPath)
    {
        pImpl->setError("Invalid input parameters");
        return false;
    }
    
    try
    {
        pImpl->clearError();
        
        // Convert to internal config format
        ProcessingConfig config(options.modelPath);
        config.useCuda = options.useCuda;
        config.numThreads = options.numThreads;
        
        // Call internal implementation
        SegmentationResult internalResult;
        bool success = pImpl->processor->processor.processVolumeFromMemory(volumeData, width, height, depth,
                                                                          isSigned, config, internalResult);
        
        if (success)
        {
            // Convert internal result to public format
            result.data = internalResult.data.release();  // Transfer ownership
            result.width = internalResult.width;
            result.height = internalResult.height;
            result.depth = internalResult.depth;
            result.isSigned = internalResult.is_signed;
            result.cropX0 = internalResult.crop_x0;
            result.cropY0 = internalResult.crop_y0;
        }
        else
        {
            pImpl->setError("Internal processing failed");
        }
        
        return success;
    }
    catch (const std::exception& e)
    {
        pImpl->setError(std::string("Exception: ") + e.what());
        return false;
    }
    catch (...)
    {
        pImpl->setError("Unknown exception occurred");
        return false;
    }
}

// Convenience interface - process volume from directory
bool SphereSeg3DProcessor::processVolumeFromDirectory(const char* inputPath,
                                                      const char* outputPath,
                                                      const ProcessingOptions& options)
{
    if (!inputPath || !outputPath || !options.modelPath)
    {
        pImpl->setError("Invalid input parameters");
        return false;
    }
    
    try
    {
        pImpl->clearError();
        
        // Convert to internal config format
        ProcessingConfig config(options.modelPath);
        config.useCuda = options.useCuda;
        config.numThreads = options.numThreads;
        
        // Call internal implementation
        return pImpl->processor->processor.processVolumeFromDirectory(inputPath, outputPath, config);
    }
    catch (const std::exception& e)
    {
        pImpl->setError(std::string("Exception: ") + e.what());
        return false;
    }
    catch (...)
    {
        pImpl->setError("Unknown exception occurred");
        return false;
    }
}



// Get last error message (if any)
const char* SphereSeg3DProcessor::getLastError() const
{
    return pImpl->lastError.c_str();
}



// Free processed volume data (call when done with result)
void SphereSeg3DProcessor::freeProcessedVolume(ProcessedVolume& volume)
{
    if (volume.data)
    {
        delete[] static_cast<uint8_t*>(volume.data);
        volume.data = nullptr;
        volume.width = volume.height = volume.depth = 0;
    }
}
