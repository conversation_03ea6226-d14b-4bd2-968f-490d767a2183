#pragma once

#ifdef SPHERESEG3D_EXPORTS
#define SPHERESEG3D_API __declspec(dllexport)
#else
#define SPHERESEG3D_API __declspec(dllimport)
#endif

// Forward declarations to hide implementation details
class SphereSeg3DProcessor_Impl;

// Simple result structure for processed volume
struct ProcessedVolume
{
    void* data;              // Processed volume data (managed internally)
    int width;               // Output width
    int height;              // Output height  
    int depth;               // Output depth
    bool isSigned;           // Data type (true=int16, false=uint16)
    int cropX0, cropY0;      // Crop offset information
    
    ProcessedVolume() : data(nullptr), width(0), height(0), depth(0), isSigned(true), cropX0(0), cropY0(0) {}
};

// Processing configuration
struct ProcessingOptions
{
    const char* modelPath;          // Path to ONNX model file
    bool useCuda;                   // Enable CUDA acceleration (default: false)
    int numThreads;                 // Number of processing threads (default: 1)

    ProcessingOptions()
        : modelPath(nullptr)
        , useCuda(false)
        , numThreads(1)
    {
    }
};



// Main SphereSeg3D processor class - simplified public interface
class SPHERESEG3D_API SphereSeg3DProcessor
{
public:
    SphereSeg3DProcessor();
    ~SphereSeg3DProcessor();

    // Primary interface - process volume data from memory
    // Returns true on success, false on failure
    bool processVolumeFromMemory(const void* volumeData,
                                int width,
                                int height, 
                                int depth,
                                bool isSigned,
                                const ProcessingOptions& options,
                                ProcessedVolume& result);

    // Convenience interface - process volume from directory
    // Returns true on success, false on failure  
    bool processVolumeFromDirectory(const char* inputPath,
                                   const char* outputPath,
                                   const ProcessingOptions& options);



    // Get last error message (if any)
    const char* getLastError() const;



    // Free processed volume data (call when done with result)
    void freeProcessedVolume(ProcessedVolume& volume);

private:
    SphereSeg3DProcessor_Impl* pImpl;  // Hide all implementation details

    // Disable copy constructor and assignment
    SphereSeg3DProcessor(const SphereSeg3DProcessor&) = delete;
    SphereSeg3DProcessor& operator=(const SphereSeg3DProcessor&) = delete;
};


