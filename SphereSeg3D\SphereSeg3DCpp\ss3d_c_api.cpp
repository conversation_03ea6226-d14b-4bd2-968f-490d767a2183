// Windows(CR LF), UTF-8
#include "SphereSeg3D.h"
#include "SphereSeg3D_C.h"

#include <cstring>
#include <cstdlib>

extern "C" {
struct SS3DContext {
    SphereSeg3D obj;
    SphereSeg3DOptions opts;
};

SS3DHandle ss3d_create(const char* model_path)
{
    try {
        auto* ctx = new SS3DContext();
        if (model_path) ctx->opts.modelPath = model_path;
        return reinterpret_cast<SS3DHandle>(ctx);
    } catch (...) {
        return nullptr;
    }
}

void ss3d_destroy(SS3DHandle h)
{
    if (h) delete reinterpret_cast<SS3DContext*>(h);
}

int ss3d_process_memory(SS3DHandle h,
                        const void* data,
                        int width,
                        int height,
                        int depth,
                        int is_signed,
                        SS3DResult* out)
{
    if (!h || !data || width<=0 || height<=0 || depth<=0 || !out) return -1;
    SS3DContext* ctx = reinterpret_cast<SS3DContext*>(h);
    SegmentationResult res;
    bool ok = ctx->obj.process_memory(data, width, height, depth, is_signed!=0, ctx->opts, res);
    if (!ok) return -2;
    out->width = res.width; out->height = res.height; out->depth = res.depth;
    out->is_signed = res.is_signed ? 1 : 0; out->crop_x0 = res.crop_x0; out->crop_y0 = res.crop_y0;
    size_t voxels = static_cast<size_t>(res.width) * res.height * res.depth;
    size_t bytes = voxels * (res.is_signed ? sizeof(int16_t) : sizeof(uint16_t));
    out->data = std::malloc(bytes);
    if (!out->data) return -3;
    std::memcpy(out->data, res.data, bytes);
    return 0;
}

void ss3d_free_result(SS3DResult* out)
{
    if (out && out->data) { std::free(out->data); out->data=nullptr; }
}
}



// Descriptive aliases
SS3DHandle ss3dCreateWithModel(const char* model_path)
{
    return ss3d_create(model_path);
}
int ss3dProcessVolumeFromMemory(SS3DHandle h,
                                const void* data,
                                int width,
                                int height,
                                int depth,
                                int is_signed,
                                SS3DResult* out)
{
    return ss3d_process_memory(h, data, width, height, depth, is_signed, out);
}
void ss3dFreeResult(SS3DResult* out)
{
    ss3d_free_result(out);
}
void ss3dDestroy(SS3DHandle h)
{
    ss3d_destroy(h);
}
