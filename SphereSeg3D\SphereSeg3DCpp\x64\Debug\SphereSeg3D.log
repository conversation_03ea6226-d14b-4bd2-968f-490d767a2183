﻿  SphereSeg3D.cpp
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\ThirdPartyLibraries\opencv-4.12.0\opencv\build\include\opencv2\imgcodecs.hpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\SphereSeg3D.cpp(228,64): warning C4100: “outputPath”: 未引用的形参
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\SphereSeg3D.cpp(1022,38): error C3861: “get_slice”: 找不到标识符
  Detector.cpp
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\ThirdPartyLibraries\opencv-4.12.0\opencv\build\include\opencv2\imgcodecs.hpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(429,31): warning C4244: “=”: 从“_Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=int64_t
          ]
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(430,31): warning C4244: “=”: 从“_Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=int64_t
          ]
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(431,31): warning C4244: “=”: 从“_Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=int64_t
          ]
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(432,31): warning C4244: “=”: 从“_Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=int64_t
          ]
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(436,33): warning C4244: “=”: 从“_Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=int64_t
          ]
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(437,33): warning C4244: “=”: 从“_Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=int64_t
          ]
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(447,36): warning C4244: “=”: 从“_Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=int64_t
          ]
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(448,35): warning C4244: “=”: 从“_Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=int64_t
          ]
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(390,111): warning C4100: “height”: 未引用的形参
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(390,100): warning C4100: “width”: 未引用的形参
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(538,36): warning C4244: “=”: 从“_Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=int64_t
          ]
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(539,35): warning C4244: “=”: 从“_Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=int64_t
          ]
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(535,12): warning C4189: “total”: 局部变量已初始化但不引用
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(533,12): warning C4189: “outputData”: 局部变量已初始化但不引用
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(612,18): warning C4244: “初始化”: 从“_Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=int64_t
          ]
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(613,18): warning C4244: “初始化”: 从“_Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=int64_t
          ]
C:\ThirdPartyLibraries\opencv-4.12.0\opencv\build\include\opencv2\core\types.hpp(1854,5): warning C4127: 条件表达式是常量
C:\ThirdPartyLibraries\opencv-4.12.0\opencv\build\include\opencv2\core\types.hpp(1854,1): message : 考虑改用 "if constexpr" 语句
C:\ThirdPartyLibraries\opencv-4.12.0\opencv\build\include\opencv2\core\types.hpp(1852,1): message : 在编译 类 模板 成员函数“_Tp cv::Rect_<_Tp>::area(void) const”时
          with
          [
              _Tp=double
          ]
C:\ThirdPartyLibraries\opencv-4.12.0\opencv\build\include\opencv2\core\types.hpp(2062,90): message : 查看对正在编译的函数 模板 实例化“_Tp cv::Rect_<_Tp>::area(void) const”的引用
          with
          [
              _Tp=double
          ]
C:\ThirdPartyLibraries\opencv-4.12.0\opencv\build\include\opencv2\core\types.hpp(1883,1): message : 查看对正在编译的 类 模板 实例化“cv::Rect_<double>”的引用
  SphereSeg3D_Public.cpp
  FocusAnalyzer.cpp
C:\ThirdPartyLibraries\opencv-4.12.0\opencv\build\include\opencv2\imgcodecs.hpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\ThirdPartyLibraries\opencv-4.12.0\opencv\build\include\opencv2\core\types.hpp(1854,5): warning C4127: 条件表达式是常量
C:\ThirdPartyLibraries\opencv-4.12.0\opencv\build\include\opencv2\core\types.hpp(1854,1): message : 考虑改用 "if constexpr" 语句
C:\ThirdPartyLibraries\opencv-4.12.0\opencv\build\include\opencv2\core\types.hpp(1852,1): message : 在编译 类 模板 成员函数“_Tp cv::Rect_<_Tp>::area(void) const”时
          with
          [
              _Tp=double
          ]
C:\ThirdPartyLibraries\opencv-4.12.0\opencv\build\include\opencv2\core\types.hpp(2062,90): message : 查看对正在编译的函数 模板 实例化“_Tp cv::Rect_<_Tp>::area(void) const”的引用
          with
          [
              _Tp=double
          ]
C:\ThirdPartyLibraries\opencv-4.12.0\opencv\build\include\opencv2\core\types.hpp(1883,1): message : 查看对正在编译的 类 模板 实例化“cv::Rect_<double>”的引用
  Segmenter.cpp
  正在生成代码...
