﻿  SphereSeg3D.cpp
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\SphereSeg3D.cpp(222,115): warning C4100: “model_path”: 未引用的形参
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\SphereSeg3D.cpp(222,70): warning C4100: “volume”: 未引用的形参
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\SphereSeg3D.cpp(243,114): warning C4100: “H”: 未引用的形参
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\SphereSeg3D.cpp(243,107): warning C4100: “W”: 未引用的形参
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\SphereSeg3D.cpp(249,77): warning C4100: “volume”: 未引用的形参
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\SphereSeg3D.cpp(256,140): warning C4100: “target_layer”: 未引用的形参
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\SphereSeg3D.cpp(319,64): warning C4100: “target_layer”: 未引用的形参
  Detector.cpp
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(305,31): warning C4244: “=”: 从“_Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=int64_t
          ]
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(306,31): warning C4244: “=”: 从“_Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=int64_t
          ]
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(307,31): warning C4244: “=”: 从“_Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=int64_t
          ]
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(308,31): warning C4244: “=”: 从“_Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=int64_t
          ]
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(312,33): warning C4244: “=”: 从“_Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=int64_t
          ]
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(313,33): warning C4244: “=”: 从“_Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=int64_t
          ]
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(323,36): warning C4244: “=”: 从“_Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=int64_t
          ]
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(324,35): warning C4244: “=”: 从“_Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=int64_t
          ]
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(266,111): warning C4100: “height”: 未引用的形参
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(266,100): warning C4100: “width”: 未引用的形参
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(408,36): warning C4244: “=”: 从“_Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=int64_t
          ]
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(409,35): warning C4244: “=”: 从“_Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=int64_t
          ]
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(405,12): warning C4189: “total”: 局部变量已初始化但不引用
D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\SphereSeg3DCpp\Detector.cpp(403,12): warning C4189: “outputData”: 局部变量已初始化但不引用
    正在创建库 D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\x64\Release\SphereSeg3D.lib 和对象 D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\x64\Release\SphereSeg3D.exp
  正在生成代码
  Previous IPDB not found, fall back to full compilation.
  All 725 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  已完成代码的生成
  SphereSeg3D.vcxproj -> D:\MyWorkSpace\Code\GitProjects\SphereSeg3D\x64\Release\SphereSeg3D.dll
