// Description:  
// IniFileReader is used to read ini file
//
// Copyright(c) 2020 - 2023 Fussen Technology Co., Ltd

#include <Windows.h>
#include <algorithm>
#include "IniFile.h"
#include "../Common/Timer.h"
#include "../Common/GLog.h"
#include <sstream>
#include "../Common/StringConvert.h"

namespace FEngine
{

	IniFile::IniFile(const std::filesystem::path& filePath, const double timeOut)
		:filePath(filePath)
		, timeOut(timeOut)
	{
	}

	void IniFile::SetTimeOut(const double seconds)
	{
		timeOut = seconds;
	}

	std::string IniFile::GetString(const std::string& section, const std::string key) const
	{
		wchar_t buf[120];
		Timer t;
		for (int iTry = 0;; ++iTry)
		{
			if (GetPrivateProfileStringW(
				__str2wstr(section).c_str(),
				__str2wstr(key).c_str(),
				NULL,
				buf,
				sizeof(buf) / sizeof(wchar_t),
				filePath.c_str()))
			{
				return __wstr2str(buf);
			}
			else
			{
				auto ec = GetLastError();
				std::string ss;
				if (!GetPrivateProfileSectionW(
					__str2wstr(section).c_str(),
					buf,
					sizeof(buf) / sizeof(wchar_t),
					filePath.c_str()))
				{
					ss = "Can not find section " + section + ". "
						+ "Fail to read " + section + "." + key + "\" from ini file " + __wstr2str(filePath)
						+ ", ErrorCode = " + std::to_string(GetLastError());
					if (!std::filesystem::exists(filePath))
						ss += ", file does not exist";
					if (!std::filesystem::exists(std::filesystem::path(filePath).parent_path()))
						ss += ", folder does not exist";
					//GLog(ss.c_str());
				}
				else
				{
					ss = "Can not find key " + key + " in section " + section + ", ErrorCode = " + std::to_string(ec);
					//GLog(ss.c_str());
				}

				if (t.Toc() < timeOut)
				{
					Timer::Sleep(retryInterval);
					continue;
				}
				else
				{
					ThrowException(ss);
				}

			}
		}
	}

	std::string IniFile::GetString(const std::string& section, const std::string key, const std::string& defaultValue) const
	{
		try
		{
			return GetString(section, key);
		}
		catch(Exception&)
		{
			//GLog("IniFileReader::GetString, missing parameter of " + key + ", using default Value: " + defaultValue);
			return defaultValue;
		}
	}

	std::wstring IniFile::GetString(const std::wstring& section, const std::wstring key) const
	{
		wchar_t buf[120];
		Timer t;
		for (int iTry = 0;; ++iTry)
		{
			std::wstring ss;
			if (GetPrivateProfileStringW(
				section.c_str(),
				key.c_str(),
				NULL,
				buf,
				sizeof(buf) / sizeof(wchar_t),
				filePath.c_str()
			))
			{
				return buf;
			}
			else
			{
				auto ec = GetLastError();
				if (!GetPrivateProfileSectionW(
					section.c_str(),
					buf,
					sizeof(buf) / sizeof(wchar_t),
					filePath.c_str()))
				{
					ss = L"Can not find section " + section + L". "
						+ L"Fail to read \"" + section + L"." + key + L"\" from ini file " + filePath
						+ L", ErrorCode = " + std::to_wstring(GetLastError());
					if (!std::filesystem::exists(filePath))
						ss += L", file does not exist";
					if (!std::filesystem::exists(std::filesystem::path(filePath).parent_path()))
						ss += L", folder does not exist";
					//GLog(ss.c_str());
				}
				else
				{
					ss = L"Can not find key " + key + L" in section " + section
						+ L", ErrorCode = " + std::to_wstring(ec);
					//GLog(ss.c_str());
				}

				if (t.Toc() < timeOut)
				{
					Timer::Sleep(retryInterval);
					continue;
				}
				else
				{
					ThrowException(ss);
				}

			}
		}

	}

	std::wstring IniFile::GetString(const std::wstring& section, const std::wstring key, const std::wstring& defaultValue) const
	{
		try
		{
			return GetString(section, key);
		}
		catch (Exception&)
		{
			//GLog(L"IniFileReader::GetString, missing parameter of " + key + L", using default Value: " + defaultValue);
			return defaultValue;
		}
	}

	bool IniFile::GetBool(const std::wstring& section, const std::wstring key) const
	{
		std::wstring value = GetString(section, key);
		transform(value.begin(), value.end(), value.begin(), ::towlower);
		if (value == L"1" || value == L"yes" || value == L"true" || value == L"on" || value == L"是")
			return true;
		else
			return false;
	}

	bool IniFile::GetBool(const std::wstring& section, const std::wstring key, const bool defaultValue) const
	{
		try
		{
			return GetBool(section, key);
		}
		catch (Exception&)
		{
			//GLog(L"IniFileReader::GetInt, missing parameter of " + key + L", using default Value: " + std::to_wstring(defaultValue));
			return defaultValue;
		}
	}

	int IniFile::GetInt(const std::string& section, const std::string key) const
	{
		return std::stoi(GetString(section, key));
	}

	int IniFile::GetInt(const std::string& section, const std::string key, const int defaultValue) const
	{
		try
		{
			return GetInt(section, key);
		}
		catch (Exception&)
		{
			//GLog("IniFileReader::GetInt, missing parameter of " + key + ", using default Value: " + std::to_string(defaultValue));
			return defaultValue;
		}
	}

	int IniFile::GetInt(const std::wstring& section, const std::wstring key) const
	{
		return std::stoi(GetString(section, key));
	}

	int IniFile::GetInt(const std::wstring& section, const std::wstring key, const int defaultValue) const
	{
		try
		{
			return GetInt(section, key);
		}
		catch (Exception&)
		{
			//GLog(L"IniFileReader::GetInt, missing parameter of " + key + L", using default Value: " + std::to_wstring(defaultValue));
			return defaultValue;
		}
	}

	float IniFile::GetFloat(const std::string& section, const std::string key) const
	{
		return std::stof(GetString(section, key));
	}

	float IniFile::GetFloat(const std::string& section, const std::string key, const float defaultValue) const
	{
		try
		{
			return GetFloat(section, key);
		}
		catch (Exception&)
		{
			//GLog("IniFileReader::GetFloat, missing parameter of " + key + ", using default Value: " + std::to_string(defaultValue));
			return defaultValue;
		}
	}

	float IniFile::GetFloat(const std::wstring& section, const std::wstring key) const
	{
		return std::stof(GetString(section, key));
	}

	float IniFile::GetFloat(const std::wstring& section, const std::wstring key, const float defaultValue) const
	{
		try
		{
			return GetFloat(section, key);
		}
		catch (Exception&)
		{
			//GLog(L"IniFileReader::GetFloat, missing parameter of " + key + L", using default Value: " + std::to_wstring(defaultValue));
			return defaultValue;
		}
	}

	bool IniFile::GetBool(const std::string& section, const std::string key) const
	{
		std::string value = GetString(section, key);
		transform(value.begin(), value.end(), value.begin(), (char(*)(char))tolower);
		if (value == "1" || value == "yes" || value == "true" || value == "on")
			return true;
		else
			return false;
	}

	std::vector<float> IniFile::GetFloatVec(const std::string& section, const std::string key) const
	{
		std::vector<float> dstVector;
		try
		{
			std::vector <std::string> stringVec;
			stringVec = SplitText(GetString(section, key));
			for (std::string& val : stringVec)
			{
				dstVector.push_back(std::stof(val));
			}
		}
		catch (std::exception& e)
		{
			ThrowException(e.what());
		}
		return dstVector;
	}

	std::vector<float> IniFile::GetFloatVec(const std::wstring& section, const std::wstring key) const
	{
		std::vector<float> dstVector;
		try
		{
			std::vector <std::string> stringVec;
			stringVec = SplitText(__wstr2str(GetString(section, key)));
			for (std::string& val : stringVec)
			{
				dstVector.push_back(std::stof(val));
			}
		}
		catch (std::exception& e)
		{
			ThrowException(e.what());
		}
		return dstVector;
	}

	std::vector<float> IniFile::GetFloatVec(const std::string& section, const std::string key, const std::string& defaultValue) const
	{
		std::vector<float> dstVector;
		try
		{
			std::vector <std::string> stringVec;
			stringVec = SplitText(GetString(section, key,defaultValue));
			for (std::string& val : stringVec)
			{
				dstVector.push_back(std::stof(val));
			}
		}
		catch (std::exception& e)
		{
			ThrowException(e.what());
		}
		return dstVector;
	}

	std::vector<std::wstring> IniFile::GetWstringVec(const std::wstring& section, const std::wstring key) const
	{
		std::vector<std::wstring> dstVector;
		try
		{
			std::vector <std::string> stringVec;
			stringVec = SplitText(__wstr2str(GetString(section, key)));
			for (std::string& val : stringVec)
			{
				dstVector.push_back(__str2wstr(val));
			}
		}
		catch (std::exception& e)
		{
			ThrowException(e.what());
		}
		return dstVector;
	}

	void IniFile::Write(const std::wstring& section, const std::wstring& key, const std::wstring& value) const
	{
		Timer t;
		for (int iTry = 0; ; ++iTry)
		{
			if (WritePrivateProfileStringW(
				section.c_str(),
				key.c_str(),
				value.c_str(),
				filePath.c_str()
			))
			{
				return;
			}
			GLog(L"Fail to write \"" + section + L"." + key + L" = " + value + L"\" to ini file " + filePath
				+ L", ErrorCode = " + std::to_wstring(GetLastError()));
			if (t.Toc() < timeOut)
				Timer::Sleep(retryInterval);
			else
				ThrowExceptionAndLog(L"Fail to write file " + filePath);
		}
	}

	void IniFile::Write(const std::string& section, const std::string& key, const std::string& value) const
	{
		Timer t;
		for (int iTry = 0; ; ++iTry)
		{
			if (WritePrivateProfileStringW(
				__str2wstr(section).c_str(),
				__str2wstr(key).c_str(),
				__str2wstr(value).c_str(),
				filePath.c_str()
			))
			{
				return;
			}
			auto ec = GetLastError();
			GLog(std::string("Fail to write \"") + section + "." + key + " = " + value + "\" to ini file " + __wstr2str(filePath)
				+ ", ErrorCode = " + std::to_string(ec));
			if (t.Toc() < timeOut)
				Timer::Sleep(retryInterval);
			else
				ThrowExceptionAndLog(L"Fail to write file " + filePath);
		}
	}

	void IniFile::DeleteKey(const std::string& section, const std::string& key) const
	{
		Timer t;
		for (int iTry = 0; ; ++iTry)
		{
			if (WritePrivateProfileStringW(
				__str2wstr(section).c_str(),
				__str2wstr(key).c_str(),
				NULL,
				filePath.c_str()
			))
			{
				return;
			}
			auto ec = GetLastError();
			GLog("Fail to delete \"" + key + "\" from ini file " + __wstr2str(filePath)
				+ ", ErrorCode = " + std::to_string(ec));
			if (t.Toc() < timeOut)
				Timer::Sleep(retryInterval);
			else
				ThrowExceptionAndLog(L"Fail to write file " + filePath);
		}

	}

	void IniFile::DeleteSection(const std::string& section) const
	{
		Timer t;
		for (int iTry = 0; ; ++iTry)
		{
			if (WritePrivateProfileSectionW(
				__str2wstr(section).c_str(),
				NULL,
				filePath.c_str()
			))
			{
				return;
			}
			GLog("Fail to delete \"" + section + "\" from ini file " + __wstr2str(filePath)
				+ ", ErrorCode = " + std::to_string(GetLastError()));
			if (t.Toc() < timeOut)
				Timer::Sleep(retryInterval);
			else
				ThrowExceptionAndLog(L"Fail to write file " + filePath);
		}
	}

	void IniFile::ClearSection(const std::string& section) const
	{
		Timer t;
		for (int iTry = 0; ; ++iTry)
		{
			if (WritePrivateProfileSectionW(
				__str2wstr(section).c_str(),
				L"",
				filePath.c_str()
			))
			{
				return;
			}
			GLog("Fail to clear \"" + section + "\" from ini file " + __wstr2str(filePath)
				+ ", ErrorCode = " + std::to_string(GetLastError()));
			if (t.Toc() < timeOut)
				Timer::Sleep(retryInterval);
			else
				ThrowExceptionAndLog(L"Fail to write file " + filePath);
		}
	}

	void IniFile::Write(const std::wstring& section, const std::wstring& key, const int value) const
	{
		Write(section, key, std::to_wstring(value));
	}

	void IniFile::Write(const std::string& section, const std::string& key, const int value) const
	{
		Write(section, key, std::to_string(value));
	}

	void IniFile::Write(const std::wstring& section, const std::wstring& key, const float value) const
	{
		Write(section, key, std::to_wstring(value));
	}

	void IniFile::Write(const std::wstring& section, const std::wstring& key, const double value) const
	{
		Write(section, key, std::to_wstring(value));
	}

	void IniFile::Write(const std::string& section, const std::string& key, const float value) const
	{
		Write(section, key, std::to_string(value));
	}

	void IniFile::Write(const std::string& section, const std::string& key, const double value) const
	{
		Write(section, key, std::to_string(value));
	}

	void IniFile::Write(const std::string& section, const std::string& key, const std::vector<float>& value) const
	{
		std::stringstream ss;
		if (value.size() > 1)
		{
			auto it = value.cbegin();
			ss << *it++;
			while (it != value.cend())
			{
				ss << "|" << *(it++);
			}
			Write(section, key, ss.str());
		}
	}

	//std::wstring IniFile::str2wstr(const std::string& s) const
	//{
	//	DWORD len = MultiByteToWideChar(CP_ACP, 0, s.data(), -1, NULL, 0);
	//	std::wstring wstr(len - 1, 0);
	//	if (MultiByteToWideChar(CP_ACP, 0, s.data(), -1, wstr.data(), len) == 0)
	//		GLog("Fail to transform " + s + " to wide string, ErrorCode = "
	//			+ std::to_string(GetLastError()));
	//	return wstr;

	//	//size_t len = 0;// including the '/0'
	//	//mbstowcs_s(&len,0,0, s.c_str(), _TRUNCATE);
	//	//std::wstring wstr(len-1,0);
	//	//mbstowcs_s(&len, wstr.data(), len, s.c_str(), _TRUNCATE);
	//	//return wstr;
	//}

	//std::string IniFile::wstr2str(const std::wstring& ws) const
	//{
	//	DWORD len = WideCharToMultiByte(CP_ACP, 0, ws.data(), -1, NULL, 0, NULL, 0);
	//	std::string mbstr(len - 1, 0);
	//	if (WideCharToMultiByte(CP_ACP, 0, ws.data(), -1, mbstr.data(), len, NULL, 0) == 0)
	//		GLog(L"Fail to transform " + ws + L" to multi-byte string, ErrorCode = "
	//			+ std::to_wstring(GetLastError()));
	//	return mbstr;

	//	//size_t len = 0;
	//	//wcstombs_s(&len, 0, 0, ws.c_str(), _TRUNCATE);
	//	//std::string mbstr(len-1, 0);
	//	//wcstombs_s(&len, mbstr.data(), len, ws.c_str(), _TRUNCATE);
	//	//return mbstr;

	//}
	std::vector<std::string> IniFile::SplitText(const std::string& s) const
	{

		std::vector <std::string > resultVec;
		char* itemString = NULL;
		char* remainString = NULL;
		itemString = strtok_s((char*)s.c_str(), "|", &remainString);
		while (itemString != NULL)
		{
			resultVec.push_back(std::string(itemString));
			itemString = strtok_s(NULL, "|", &remainString);
		}

		return resultVec;

	}
}
