#include "SphereSeg3DCpp/SphereSeg3D_New.h"
#include <iostream>

int main()
{
    std::cout << "Testing new SphereSeg3D API...\n";
    
    SphereSeg3D processor;
    
    std::string input_path = "./data/processed/Slice_t";
    std::string output_path = "./output";
    std::string model_path = "./x64/Release/Config/bestV5.onnx";
    
    bool success = processor.process_volume(input_path, output_path, model_path);
    
    if (success)
    {
        std::cout << "Processing completed successfully!\n";
        
        // Print timing information
        std::cout << "\nDetailed timing:\n";
        std::cout << "  Data load: " << processor.timing.data_load_time << "s\n";
        std::cout << "  Focus: " << processor.timing.focus_time << "s\n";
        std::cout << "  AI: " << processor.timing.ai_infer_time << "s\n";
        std::cout << "  Segmentation: " << processor.timing.segmentation_time << "s\n";
        std::cout << "  Save: " << processor.timing.save_time << "s\n";
        std::cout << "  Total: " << processor.timing.total_time << "s\n";
    }
    else
    {
        std::cout << "Processing failed!\n";
        return 1;
    }
    
    return 0;
}
