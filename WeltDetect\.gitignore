# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyTorch
*.pth
*.pt
!models/*.pt  # 保留模型文件

# Jupyter Notebook
.ipynb_checkpoints

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Data files (large files)
output/
data
*.rec
*.rec2
temp/
tmp/

# Logs
*.log
logs/
output*
# Config (if contains sensitive info)
# config/local_config.yaml

# Test files
tests/
test_*.py
*_test.py

# Old/backup files
*_old.py
*_backup.py
*_complex.py
main_old.py
main_complex.py
main_clean.py

# Scripts (development only)
scripts/

# Environment setup
activate_env.sh
ENVIRONMENT_SETUP.md
