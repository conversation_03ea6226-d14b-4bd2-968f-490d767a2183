# WeltDetect - 工业CT小球分割系统

基于深度学习和3D区域生长的工业CT小球自动分割系统。

## 项目目的

本系统用于工业CT扫描数据中小球（如钢球、陶瓷球等）的自动检测和精确分割，主要应用场景：

- **质量检测**: 检测小球的完整性、缺陷、尺寸等
- **计数统计**: 自动统计小球数量和分布
- **3D重建**: 提取小球的精确3D形状用于进一步分析
- **批量处理**: 处理大量CT数据，提高检测效率

## 算法原理

### 核心思路
1. **对焦与校正**：自动对焦得到全局基准层；基于角点插值与网格拉平，校正倾斜后再检测
2. **2D目标检测**：在基准层与网格拉平的合成图上进行检测，获得稳定的检测框
3. **3D分割**：以检测框为约束，按类别采用球/柱形局部阈值分割，得到精确球体掩膜

### 关键算法步骤

#### 1. 数据预处理
- **自动对焦**: 基于梯度方差找到最清晰的CT层
- **数据标准化**: 统一不同设备的CT值范围
- **格式转换**: 支持多种CT数据格式（bin、PNG序列等）

#### 2. 2D目标检测
- **第一阶段**：在全局基准层上检测
- **倾斜校正**：选择四个角落的0类框估 z，拟合平面 z(x,y)；在0类外接框内网格划分并按各格 z 取局部拼接成拉平的合成图
- **第二阶段**：在合成图上复检，得到水平校正后的检测框

#### 3. 种子点生成
- **中心计算**: 以检测框中心作为3D种子点
- **层间对应**: 建立2D检测与3D位置的对应关系
- **有效性验证**: 确保种子点位于有效的CT值范围内

#### 4. 3D分割
- **类0/2（单圆/小异常）**：在球心层ROI二值化细化球心与直径；按细化后的球体约束在3D内二值分割
- **类1（粘连）**：在检测框XY、以类0平均边长为Z厚度的长方体内二值分割
- **类3（大外接）**：用于过滤，不参与分割
- **空洞与平滑**：连通域修补空洞，边界平滑

#### 5. 后处理与输出
- **体积过滤**: 去除过小的噪声区域（默认<100像素）
- **格式转换**: 输出ImageJ兼容的.raw格式
- **统计分析**: 生成球体数量、体积等统计信息

### 技术特点
- **混合方法**: 结合深度学习的快速检测和传统方法的精确分割
- **参数可调**: 支持根据不同材质和扫描条件调节分割参数
- **内存优化**: 串行处理避免大数据量的内存溢出
- **多格式支持**: 兼容多种CT数据格式和输出格式

## 系统架构

```
WeltDetect/
├── main.py                 # 主程序入口（简洁版）
├── main_old.py            # 原始版本（包含自动安装依赖等功能）
├── main_complex.py        # 复杂版本（包含详细调试和可视化）
├── read_rec2.py           # rec2格式数据读取工具
├── src/                    # 核心模块
│   ├── data_loader.py      # 数据加载模块
│   ├── auto_focus.py       # 自动对焦模块
│   ├── ai_inference.py     # AI推理模块
│   ├── sphere_segmentation.py  # 3D球体分割模块
│   └── utils.py           # 工具函数
├── config/
│   └── config.yaml        # 配置文件
├── models/
│   └── best.pt           # YOLO模型文件
└── data/
    ├── raw/              # 原始数据
    ├── processed/        # 处理后数据
    └── output/           # 输出结果
```

## 文件版本说明

### main.py版本对比
- **main.py**: 当前使用的简洁版本，去除冗余输出，适合生产环境
- **main_old.py**: 最初版本，包含自动安装依赖、详细注释等功能
- **main_complex.py**: 功能完整版本，包含详细的调试信息、可视化、统计分析等

### 数据处理工具
- **read_rec2.py**: 专门用于处理rec2格式的CT数据文件，支持PNG提取和格式转换

## 模块功能

### 1. 数据加载模块 (data_loader.py)
- 支持bin格式数据（配合ImageParam.ini）
- 支持PNG序列数据
- 自动检测数据格式并加载

### 2. 自动对焦模块 (auto_focus.py)
- 基于梯度方差的对焦评估
- 自动寻找最佳对焦层
- 支持多峰值检测

### 3. AI推理模块 (ai_inference.py)
- YOLO目标检测
- 支持GPU/CPU推理
- 检测框后处理

### 4. 3D球体分割模块 (sphere_segmentation.py)
- 固定类别策略：0/2 按球体约束，1 按柱状约束，3 仅用于过滤
- 细化球心与直径：在球心层进行2D二值细化，提升圆度
- 3D阈值：仅在球/柱约束内自适应阈值
- 输出RAW：processed_allSlice_{W}x{H}x{Z}.raw（全层）
- 输出聚焦层PNG：focus_layer_segmentation.png


## 快速开始

### 1. 环境配置
```bash
# 创建环境
conda create -n sphereSeg python=3.10 -y
conda activate sphereSeg
pip install -r requirements.txt
```

### 2. 配置文件
编辑 `config/config.yaml`:
```yaml
paths:
  model_path: "./models/best.pt"
  raw_data: "./data/raw"
  processed_data: "./data/processed"
  output: "./data/output"

device:
  name: "cuda"  # 或 "cpu", "mps"
```

### 3. 运行程序
```bash
# 调试模式（详细输出）
conda run -n sphereSeg python -u main.py -i ./data/processed/Slice_t -d

# 生产模式（快速运行）
conda run -n sphereSeg python -u main.py -i ./data/processed/Slice_t --production
```

## 参数说明（当前固定工作流）

- `--input/-i`: 输入数据路径（支持 bin/PNG 目录/vgi+rec/rec）
- `--debug/-d`: 调试模式，详细日志
- `--production`: 生产模式，简洁日志

说明：分割方法固定为二值化；保存固定为随机背景；球形约束固定启用。其余历史参数（如 seg-method、random-bg、sphere-constraint、intensity-low、max-radius 等）已移除。

## 数据格式

### 输入数据
1. **bin格式**: 需要ImageParam.ini配置文件
2. **PNG序列**: 按层编号的PNG文件
3. **rec2格式**: 使用read_rec2.py工具预处理

### 输出数据
1. **3D分割结果**: `.raw`格式，可用ImageJ打开
2. **聚焦层PNG**: `focus_layer_segmentation.png`，来自最终分割体在聚焦层的切片


## rec2数据处理

如果你有新的rec2格式数据，使用read_rec2.py进行预处理：

```bash
# 提取rec2文件中的PNG图像
python read_rec2.py

# 然后使用提取的PNG数据运行主程序
python main.py --input ./extracted_png_folder
```

read_rec2.py功能：
- 从rec2文件中提取PNG图像序列
- 处理缺失PNG签名的情况
- 自动保存为标准PNG格式

## 二次开发

### 添加新的数据格式
在 `src/data_loader.py` 中添加新的加载方法：
```python
def load_new_format(self, data_path):
    # 实现新格式的加载逻辑
    pass
```

### 修改分割算法
在 `src/sphere_segmentation.py` 中修改 `simple_region_growing` 方法：
```python
def simple_region_growing(self, volume_data, seed_point, detection_box):
    # 实现新的分割算法
    pass
```

### 添加新的后处理
在 `src/sphere_segmentation.py` 中添加后处理方法：
```python
def post_process(self, mask):
    # 实现后处理逻辑
    pass
```

## 常见问题

### 1. 模型文件不存在
确保 `models/best.pt` 文件存在，或在配置文件中修改路径。

### 2. 内存不足
- 减少 `max_radius` 参数
- 使用生产模式（`--production`）
- 分批处理数据

### 3. 分割效果不佳
- 调整 `intensity_low` 和 `intensity_high` 参数
- 修改 `min_size` 过滤小噪声
- 检查输入数据质量

### 4. 运行速度慢
- 使用GPU加速（设置device为"cuda"）
- 使用生产模式跳过调试输出
- 减少 `max_radius` 参数

## 性能优化

### 典型运行时间（257个球体）
- 数据加载: ~0.2秒
- 自动对焦: ~0.9秒
- AI推理: ~1.0秒
- 3D分割: ~17秒
- 数据保存: ~10秒
- **总计**: ~30秒

### 优化建议
1. 使用GPU加速AI推理
2. 生产模式跳过调试输出
3. 合理设置分割参数
4. 预处理数据格式

## 许可证

MIT License

