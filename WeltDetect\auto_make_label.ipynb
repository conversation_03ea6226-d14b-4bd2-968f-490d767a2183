{"cells": [{"cell_type": "code", "execution_count": 17, "id": "408b5272", "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "import cv2\n", "import numpy as np\n", "from src.ai_inference  import *\n", "def save_yolo_to_labelme(result, image_path, class_names, save_path):\n", "    \"\"\"\n", "    将 YOLOv5 格式结果转换为 LabelMe JSON 格式并保存\n", "    \n", "    Args:\n", "        result (np.n<PERSON>ray): [N, 6] -> (x_center, y_center, w, h, conf, class_id)\n", "        image_path (str): 原始图像路径\n", "        class_names (list): 类别名称列表\n", "        save_path (str): json 保存路径\n", "    \"\"\"\n", "    img = cv2.imread(image_path)\n", "    h, w = img.shape[:2]\n", "\n", "    shapes = []\n", "    for det in result:\n", "        # print(det)\n", "        x_c, y_c, bw, bh, conf, cls_id = det\n", "        # print(det)\n", "        cls_id = int(cls_id)\n", "        label = class_names[cls_id]\n", "        # 转换为 x1y1x2y2\n", "        x1 = round(float(x_c), 3) # round(float(max(0, (x_c - bw / 2))), 3)\n", "        y1 = round(float(y_c), 3) # round(float(max(0, (y_c - bh / 2))), 3)\n", "        x2 = round(float(bw), 3) # round(float(min(w - 1, (x_c + bw / 2))), 3)\n", "        y2 = round(float(bh), 3) # round(float(min(h - 1, (y_c + bh / 2))), 3)\n", "         \n", "        shape = {\n", "            \"label\": label,\n", "            \"points\": [[x1, y1], [x2, y2]],\n", "            \"group_id\": cls_id,\n", "            \"description\": \"\",\n", "            \"shape_type\": \"rectangle\",\n", "            \"flags\": {},\n", "            \"mask\": None,\n", "        }\n", "        shapes.append(shape)\n", "\n", "    data = {\n", "        \"version\": \"5.4.1\",\n", "        \"flags\": {},\n", "        \"shapes\": shapes,\n", "        \"imagePath\": os.path.basename(image_path),\n", "        \"imageData\": None,\n", "        \"imageHeight\": h,\n", "        \"imageWidth\": w\n", "    }\n", "\n", "    with open(save_path, \"w\", encoding=\"utf-8\") as f:\n", "        json.dump(data, f, indent=2, ensure_ascii=False)\n", "\n", "    print(f\"✅ Saved labelme annotation to {save_path}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "4488dcfd", "metadata": {}, "outputs": [], "source": ["DEVICE = \"cuda:0\"\n", "WEIGHT_PATH = r\"/data/lijunlin/project/yolov5-master/runs/train/exp36/weights/best.pt\"\n", "config = {}\n", "config['confidence_threshold'] = 0.35\n", "config['iou_threshold'] = 0.2\n", "class_names = [\"welt\", \"welt_mix\", \"welt_rosin\", \"all\"]\n", "folder_path = '/data/lijunlin/data/CT/PCB/wait2mark/'\n", "save_folder = r\"/data/lijunlin/data/CT/PCB/labelme/\"\n", "os.makedirs(save_folder, exist_ok=True)\n", "model = ai_init( WEIGHT_PATH, DEVICE)\n", "file_name_list = os.listdir(folder_path)"]}, {"cell_type": "code", "execution_count": null, "id": "108daa17", "metadata": {}, "outputs": [], "source": ["for file_name in file_name_list:\n", "    image_path = folder_path + file_name\n", "    save_name = image_path.split(\"/\")[-1][:-3] + \"json\"\n", "    image = cv2.imread( image_path, -1)\n", "    result = inference(model, image[:,:,0], DEVICE, config)\n", "    # 保存为 labelme json\n", "    save_yolo_to_labelme(result[0], image_path, class_names, save_folder + save_name)"]}, {"cell_type": "markdown", "id": "1fd643e2", "metadata": {}, "source": ["# One"]}, {"cell_type": "code", "execution_count": null, "id": "4c598d28", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Fusing layers... \n", "YOLOv5s summary: 214 layers, 7030417 parameters, 0 gradients, 16.0 GFLOPs\n"]}, {"name": "stdout", "output_type": "stream", "text": ["使用设备: cuda:0\n"]}], "source": ["# 调用AI\n", "DEVICE = \"cuda:0\"\n", "WEIGHT_PATH = r\"/data/lijunlin/project/yolov5-master/runs/train/exp36/weights/best.pt\"\n", "model = ai_init( WEIGHT_PATH, DEVICE)"]}, {"cell_type": "code", "execution_count": null, "id": "8f482959", "metadata": {}, "outputs": [{"data": {"text/plain": ["(193, 6)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["config = {}\n", "config['confidence_threshold'] = 0.35\n", "config['iou_threshold'] = 0.2\n", "image_path = '/data/lijunlin/data/CT/PCB/images/val/0067_12500.442_18014.914.jpg'\n", "image = cv2.imread(image_path)\n", "result = inference(model, image[:,:,0], DEVICE, config)\n", "result[0].shape"]}, {"cell_type": "code", "execution_count": null, "id": "6672838f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Saved labelme annotation to 0067_12500.442_18014.914.json\n"]}], "source": ["# 类别名\n", "save_name = image_path.split(\"/\")[-1][:-3] + \"json\"\n", "class_names = [\"welt\", \"welt_mix\", \"welt_rosin\", \"all\"]\n", "# 保存为 labelme json\n", "save_yolo_to_labelme(result[0], \"/data/lijunlin/data/CT/PCB/images/val/0067_12500.442_18014.914.jpg\", class_names, save_name)"]}, {"cell_type": "code", "execution_count": 1, "id": "b644d73a", "metadata": {}, "outputs": [], "source": ["import math\n", "def nearest_power_of_two(n):\n", "    \"\"\"返回最接近n的2的幂次方\"\"\"\n", "    if n <= 0:\n", "        return 1\n", "    # 计算log2(n)\n", "    log2 = math.log2(n)\n", "    # 找到相邻的两个幂次方\n", "    lower_pow = int(log2)\n", "    higher_pow = lower_pow + 1\n", "    # 计算对应的数值\n", "    lower_val = 2 ** lower_pow\n", "    higher_val = 2 ** higher_pow\n", "    # 判断哪个更接近\n", "    # return (higher_val + lower_val)//2\n", "    # if abs(n - lower_val) < abs(n - higher_val):\n", "    #     return lower_val\n", "    # else:\n", "    #     return higher_val\n", "    if n == lower_val:\n", "        return lower_val\n", "    if n == higher_val:\n", "        return higher_val\n", "    \n", "    return (higher_val + lower_val)//2"]}, {"cell_type": "code", "execution_count": null, "id": "3197399c", "metadata": {}, "outputs": [], "source": ["os.makedirs(\"output\", exist_ok=True)"]}], "metadata": {"kernelspec": {"display_name": "det", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}