# 工业CT小球分割系统
import cv2
import numpy as np
import time
import os

from src.data_loader import DataLoader
from src.auto_focus import AutoFocusProcessor
from src.ai_inference import InferenceProcessor
from src.sphere_segmentation import SphereSegmentation
from src.utils import load_config, ensure_directories, validate_model_file, get_color_for_class

def load_processed_data(data_path):
    """加载处理后的数据"""
    if not os.path.exists(data_path):
        print(f"错误: 数据路径不存在: {data_path}")
        return None

    ini_file = os.path.join(data_path, "ImageParam.ini")
    if os.path.exists(ini_file):
        print(f"加载bin格式数据: {data_path}")
        try:
            config = load_config()
            data_loader = DataLoader(config)
            volume_data = data_loader.load_volume_from_ini(data_path)
            print(f"数据加载成功，形状: {volume_data.shape}")
            return volume_data
        except Exception as e:
            print(f"数据加载失败: {e}")
            return None

    # vgi+rec: 传入的是vgi文件路径
    if os.path.isfile(data_path) and data_path.lower().endswith('.vgi'):
        try:
            print(f"加载REC格式数据: {data_path}")
            config = load_config()
            data_loader = DataLoader(config)
            volume_data = data_loader.load_volume_from_vgi_rec(data_path)
            print(f"数据加载成功，形状: {volume_data.shape}")
            return volume_data
        except Exception as e:
            print(f"数据加载失败: {e}")
            return None

    # 直接传入rec
    if os.path.isfile(data_path) and data_path.lower().endswith('.rec'):
        try:
            print(f"加载REC格式数据: {data_path}")
            config = load_config()
            data_loader = DataLoader(config)
            volume_data = data_loader.load_volume_from_rec(data_path)
            print(f"数据加载成功，形状: {volume_data.shape}")
            return volume_data
        except Exception as e:
            print(f"数据加载失败: {e}")
            return None

    # 目录中存在PNG
    png_files = [f for f in os.listdir(data_path)] if os.path.isdir(data_path) else []
    png_files = [f for f in png_files if f.lower().endswith('.png')]
    png_files.sort( key = lambda x: float(x.split('_')[-1][:-4]))

    if png_files:
        print(f"加载PNG格式数据: {data_path}")
        try:
            from src.auto_focus import load_volume_from_png_folder
            volume_data = load_volume_from_png_folder(data_path)
            print(f"数据加载成功，形状: {volume_data.shape}")
            return volume_data
        except Exception as e:
            print(f"数据加载失败: {e}")
            return None

    print(f"错误: 未找到支持的数据格式")
    return None

# Legacy/Inactive: overlap analysis is not used in the current workflow
# def check_detection_overlap(detections, debug_mode=True):
#     """Check detection box overlap (unused)."""
#     if not debug_mode:
#         return 0
#     overlap_count = 0
#     for i, det1 in enumerate(detections):
#         x1_1, y1_1, x2_1, y2_1 = map(int, det1[:4])
#         for j, det2 in enumerate(detections[i+1:], i+1):
#             x1_2, y1_2, x2_2, y2_2 = map(int, det2[:4])
#             if not (x2_1 < x1_2 or x2_2 < x1_1 or y2_1 < y1_2 or y2_2 < y1_1):
#                 overlap_count += 1
#     return overlap_count

# Legacy/Inactive: seed generation is not used in the current workflow
# def generate_seed_points(detections, target_layer, debug_mode=True):
#     """Generate seed points from detections (unused)."""
#     seed_points = []
#     for det in detections:
#         x1, y1, x2, y2 = map(int, det[:4])
#         center_x = (x1 + x2) // 2
#         center_y = (y1 + y2) // 2
#         seed_points.append((target_layer, center_y, center_x))
#     return seed_points

def create_sphere_segmenter(debug_mode=True, **params):
    """创建球体分割器"""
    default_params = {
        'gradient_threshold': 50,
        'intensity_tolerance_low': 0.85,
        'max_radius': 8,
        'debug_mode': debug_mode,
        'use_spherical_constraint': True  # 固定启用球形约束
    }

    default_params.update(params)

    return SphereSegmentation(**default_params)

def run_infer( slice_list, inference, model_available, peak_indices, debug_mode = True):
    """
    param:
        slice_list:    数据
        model:          模型
        config:         读取的配置文件
        model_available:
        peak_indices:   聚焦层idx
        all_scores:     所有聚焦层得分
    return:
        target_layer: 最清晰的目标聚焦层
        detections:   推理的坐标 [ x, y, x1, y1, conf, class ]
    """

    detections = []
    detect_number = -1
    most_detect_layer_idx = -1

    # 多峰时使用批量推理，单峰时直接返回该峰
    if len(peak_indices) > 1:
        if model_available and inference is not None:
            try:
                # 准备批量数据
                batch_data = np.stack([slice_list[idx] for idx in peak_indices], axis=0)
                ai_start = time.time()

                # 批量推理
                batch_results = inference.inference_batch(batch_data)
                ai_time = time.time() - ai_start

                if debug_mode:
                    print(f"多峰批量推理完成，耗时: {ai_time:.2f}秒，处理{len(peak_indices)}层")

                # 处理批量结果
                for i, result in enumerate(batch_results):
                    if result is not None and len(result) > 0:
                        detections.append(result)
                        if len(result) >= detect_number:
                            detect_number = len(result)
                            most_detect_layer_idx = i

                        if debug_mode:
                            print(f"层{peak_indices[i]}: 检测到 {len(result)} 个目标")
                    else:
                        detections.append([])

            except Exception as e:
                if debug_mode:
                    print(f"批量推理失败，回退到单张推理: {e}")
                # 回退到原有的单张推理逻辑
                detections = []
                detect_number = -1
                most_detect_layer_idx = -1
    else:
        # 单峰情况：直接选择该峰，无需推理
        if debug_mode:
            print(f"单峰检测，直接选择层{peak_indices[0]}，跳过推理")
        target_layer = peak_indices[0]
        return target_layer, None, None, None

    # 如果批量推理失败或只有单层，使用原有的单张推理逻辑
    if len(detections) == 0:
        count = 0
        for target_layer in peak_indices:

            if debug_mode:
                print(f"\n处理以第{target_layer}层拟合的平面图...")

            slice_data = slice_list[target_layer]  # 修正：直接使用target_layer作为索引

            if debug_mode:
                print(f"\n开始AI推理...")
            ai_start = time.time()

            if model_available and inference is not None:
                try:
                    result = inference.inference(slice_data)  # 修正：调用inference方法

                    if result and len(result) > 0 and len(result[0]) > 0:
                        if hasattr(result[0], 'cpu'):
                            detections.append(result[0].cpu().numpy())
                        else:
                            detections.append(result[0])

                        if len(result[0]) >= detect_number:
                            detect_number = len(result[0])
                            most_detect_layer_idx = count

                    else:
                        detections.append([])

                    ai_time = time.time() - ai_start

                    if debug_mode:
                        print(f"单张AI推理完成，耗时: {ai_time:.2f}秒")
                        print(f"检测到 {len(detections[-1])} 个目标")

                except Exception as e:
                    ai_time = time.time() - ai_start
                    if debug_mode:
                        print(f"AI推理失败: {e}")
                    detections.append([])
            else:
                ai_time = 0
                if debug_mode:
                    print("跳过AI推理")

            count += 1

    if len(detections) == 0 or most_detect_layer_idx == -1:
        return None, None, None, None

    target_layer = peak_indices[most_detect_layer_idx]
    detections   = detections[most_detect_layer_idx]

    return target_layer, detections, slice_list[most_detect_layer_idx], most_detect_layer_idx

# ---- 倾斜校正：角点插值 + 网格拉平 + 复检 ----
# def _select_corner_boxes_class0(detections, W, H):
#     dets = [d for d in detections if int(d[-1]) == 0]
#     if len(dets) == 0:
#         return []
#     corners = [(0,0), (W-1,0), (0,H-1), (W-1,H-1)]
#     chosen = []
#     used = set()
#     for cx0, cy0 in corners:
#         best_i, best_d = -1, 1e18
#         for i, d in enumerate(dets):
#             x1,y1,x2,y2 = map(int, d[:4])
#             cx = (x1+x2)//2; cy = (y1+y2)//2
#             dist = (cx-cx0)*(cx-cx0) + (cy-cy0)*(cy-cy0)
#             if dist < best_d and i not in used:
#                 best_d = dist; best_i = i
#         if best_i >= 0:
#             chosen.append(dets[best_i]); used.add(best_i)
#     # 可能会有重复或不足4个
#     return chosen


# def _best_z_for_cube(volume, z_center, x0, y0, side_xy, side_z, focus_proc):
#     Z, H, W = volume.shape
#     half_z = max(1, side_z//2)
#     z0 = max(0, int(z_center - half_z))
#     z1 = min(Z, int(z0 + side_z))
#     x0 = max(0, x0); y0 = max(0, y0)
#     x1 = min(W, x0 + side_xy); y1 = min(H, y0 + side_xy)
#     if x1 <= x0 or y1 <= y0 or z1 <= z0:
#         return int(np.clip(z_center, 0, Z-1))
#     # 在该立方体内对每个 z 计算聚焦度（Tenengrad）
#     best_z, best_score = z0, -1e18
#     for z in range(z0, z1):
#         tile = volume[z, y0:y1, x0:x1]
#         score = focus_proc.cal_score(tile)
#         if score > best_score:
#             best_score = score; best_z = z
#     return int(best_z)


# def _fit_plane(points):
#     # points: list of (x,y,z)
#     A = []
#     b = []
#     for x,y,z in points:
#         A.append([x, y, 1.0])
#         b.append(z)
#     A = np.array(A, dtype=np.float64)
#     b = np.array(b, dtype=np.float64)
#     try:
#         coef, _, _, _ = np.linalg.lstsq(A, b, rcond=None)
#         a,b_,c = coef.tolist()
#         return a, b_, c
#     except Exception:
#         return 0.0, 0.0, float(np.median([p[2] for p in points]))


def _interp_z(a,b,c, x,y, Z):
    z = a*x + b*y + c
    return int(np.clip(round(z), 0, Z-1))


def tilt_flatten_and_redetect(
        volume_data,
        detections,
        z_func,
        processor,
        grid_n=4,
        debug=False,
    ):
    # 1) 选角点 0 类框
    Z,H,W = volume_data.shape
    a,b,c = z_func
    if debug:
        print(f"使用拟合平面redetect: z = {a:.4f} x + {b:.4f} y + {c:.2f}")
    # 4) 取 0 类最小外接矩形
    class0 = [d for d in detections if int(d[-1])==0]
    if len(class0)==0:
        return detections
    xs1 = [int(d[0]) for d in class0]; ys1=[int(d[1]) for d in class0]
    xs2 = [int(d[2]) for d in class0]; ys2=[int(d[3]) for d in class0]
    rx0, ry0 = max(0, min(xs1)), max(0, min(ys1))
    rx1, ry1 = min(W, max(xs2)), min(H, max(ys2))
    if rx1<=rx0 or ry1<=ry0:
        return detections
    # 5) 网格分块并构建拉平合成图
    gw = max(1, (rx1-rx0)//grid_n)
    gh = max(1, (ry1-ry0)//grid_n)
    gridComposite = np.zeros((ry1-ry0, rx1-rx0), dtype=volume_data.dtype)
    grid_z = np.zeros((grid_n, grid_n), dtype=np.int32)
    for gy in range(grid_n):
        for gx in range(grid_n):
            x_start = rx0 + gx*gw; x_end = rx0 + (gx+1)*gw if gx<grid_n-1 else rx1
            y_start = ry0 + gy*gh; y_end = ry0 + (gy+1)*gh if gy<grid_n-1 else ry1
            cx = (x_start + x_end)//2; cy=(y_start+y_end)//2
            zi = _interp_z(a,b,c, cx,cy, Z)
            grid_z[gy,gx] = zi
            gridComposite[y_start-ry0:y_end-ry0, x_start-rx0:x_end-rx0] = volume_data[zi, y_start:y_end, x_start:x_end]
    # 6) 在合成图上复检
    result = processor.inference( gridComposite)
    if not result or len(result)==0:
        return detections
    det = result[0]
    # 7) 把马赛克检测框还原到全图坐标，并用所在网格的 z 赋值
    det_out = []
    for row in det:
        x1,y1,x2,y2,conf,cls = row.tolist()
        gx = min(grid_n-1, max(0, (int((x1+x2)//2))//gw))
        gy = min(grid_n-1, max(0, (int((y1+y2)//2))//gh))
        zi = int(grid_z[gy,gx])
        det_out.append([x1+rx0, y1+ry0, x2+rx0, y2+ry0, zi, conf, cls])
    return np.array(det_out, dtype=np.float32)


def _filter_by_class3_bigbox(dets, W, H):
    print("_filter_by_class3_bigbox")
    if dets is None or len(dets) == 0:
        return dets
    npd = np.array(dets)
    # 识别所有类3框，合成一个大框
    cls = npd[:, -1].astype(int) if npd.ndim == 2 and npd.shape[1] >= 6 else np.array([], dtype=int)
    idx3 = np.where(cls == 3)[0]
    if idx3.size == 0:
        return dets
    xs1 = npd[idx3, 0].astype(int); ys1 = npd[idx3, 1].astype(int)
    xs2 = npd[idx3, 2].astype(int); ys2 = npd[idx3, 3].astype(int)



    bx1 = max(0, int(xs1.min())); by1 = max(0, int(ys1.min()))
    bx2 = min(W, int(xs2.max())); by2 = min(H, int(ys2.max()))
    # 过滤框中心不在大框内的其它类别
    kept = []
    for row in npd:

        x1,y1,x2,y2 = map(int, row[:4])
        cx = (x1 + x2) // 2  # 框中心x坐标
        cy = (y1 + y2) // 2  # 框中心y坐标
        if cx >= bx1 and cy >= by1 and cx <= bx2 and cy <= by2:
            kept.append(row.tolist())

    kept = np.array(kept, dtype=np.float32) if len(kept)>0 else npd

    # 过滤后重新计算大框
    x_min, y_min = kept[:, 0].min(), kept[:, 1].min()
    x_max, y_max = kept[:, 2].max(), kept[:, 3].max()

    for row in kept:
        if row[-1] == 3:
            row[:4] = [x_min, y_min, x_max, y_max]

    return kept

def main(input_data_path=None, debug_mode=True, **segmentation_params):
    """主程序"""
    total_start_time = time.time()

    print("WeltDetect - 工业CT小球分割系统")
    print("=" * 40)

    if debug_mode:
        print("调试模式: 详细输出")
    else:
        print("生产模式: 仅最终结果")

    # 加载配置
    config = load_config()
    if input_data_path is None:
        input_data_path = "./data/processed/Slice_t"

    ensure_directories(config)
    model_available = validate_model_file(config['paths']['model_path'])

    # 初始化处理器
    auto_focus_processor = AutoFocusProcessor(config, debug_mode)
    sphere_segmenter = create_sphere_segmenter(debug_mode, **segmentation_params)

    infer_process = None
    ai_init_time = 0.0
    if model_available:
        if debug_mode:
            print("初始化AI模型...")
        t0 = time.time()
        infer_process = InferenceProcessor(config)
        ai_init_time = time.time() - t0

    # 1. 数据加载
    if debug_mode:
        print("\n开始数据加载...")
    data_load_start = time.time()
    volume_data = load_processed_data(input_data_path)
    data_load_time = time.time() - data_load_start

    if volume_data is None:
        print("错误: 数据加载失败")
        return None

    # 兼容 PNG 序列可能出现的单通道维度，统一为 (Z,H,W)
    if isinstance(volume_data, np.ndarray) and volume_data.ndim == 4 and volume_data.shape[-1] == 1:
        volume_data = volume_data[..., 0]

    if debug_mode:
        print(f"数据加载完成，耗时: {data_load_time:.2f}秒")


    # 2. 一体化检测+精确z（新管线）
    # ========== Z层精细化检测模块 ==========
    if debug_mode:
        print("\n" + "="*60)
        print("【Z层精细化检测模块】")
        print("="*60)
    zref_start = time.time()
    roi_box, composite, detections = auto_focus_processor.detect_balls_precise(volume_data, infer_process)
    zref_time = time.time() - zref_start
    if debug_mode:
        print(f"\n【模块总耗时】: {zref_time:.2f}秒")
        print("="*60)

    if detections is None or len(detections) == 0:
        print("没有检测到目标")
        return None

    # ========== 3D分割模块 ==========
    Z, H, W = volume_data.shape
    if debug_mode:
        print(f"\n【3D分割模块】")
        print(f"  检测到目标数量: {len(detections)}个")

    # 使用 detect_balls_precise 导出的外接框裁剪体数据（复合图与检测框已是局部坐标，不需再修正）
    rx0, ry0, rx1, ry1 = roi_box
    if debug_mode:
        print(f"  ROI区域: X[{rx0}:{rx1}], Y[{ry0}:{ry1}]")
        print(f"  复合图尺寸: {composite.shape}")
        print(f"  体数据裁剪: {volume_data.shape[0]}×{ry1-ry0}×{rx1-rx0}")

    volume_data = volume_data[:, ry0:ry1, rx0:rx1]
    # 注意：detections 与 composite 已经是局部坐标系，此处无需再平移修正

    segmentation_start = time.time()
    # 使用二值分割方法
    sphere_masks, _ = sphere_segmenter.segment_spheres_binary(volume_data, detections, composite)
    segmentation_time = time.time() - segmentation_start

    # 规范化每个mask的尺寸，以匹配当前volume_data
    Z,H,W = volume_data.shape
    fixed_masks = []
    for m in sphere_masks:
        if m.shape == (Z,H,W):
            fixed_masks.append(m)
        else:
            z2 = min(Z, m.shape[0]); h2 = min(H, m.shape[1]); w2 = min(W, m.shape[2])
            mm = np.zeros((Z,H,W), dtype=bool)
            mm[:z2, :h2, :w2] = m[:z2, :h2, :w2]
            fixed_masks.append(mm)
    sphere_masks = fixed_masks

    if len(sphere_masks) == 0:
        if debug_mode:
            print(f"  分割失败: 未生成有效球体")
        return None

    if debug_mode:
        print(f"  分割完成: {segmentation_time:.2f}秒")
        print(f"  成功生成: {len(sphere_masks)}个球体")

    # 统计每个球体mask在z方向的覆盖范围
    if len(sphere_masks) > 0:
        z_ranges = []
        for i, mask in enumerate(sphere_masks):
            z_indices = np.where(np.any(mask, axis=(1, 2)))[0]
            if len(z_indices) > 0:
                z_min, z_max = int(z_indices[0]), int(z_indices[-1])
                z_ranges.append((z_min, z_max, z_max - z_min + 1))
            else:
                z_ranges.append((0, 0, 0))

        # 统计整体z范围
        all_z_mins = [r[0] for r in z_ranges if r[2] > 0]
        all_z_maxs = [r[1] for r in z_ranges if r[2] > 0]
        all_z_spans = [r[2] for r in z_ranges if r[2] > 0]

        if all_z_mins:
            print(f"球体mask z范围统计:")
            print(f"  整体z范围: {min(all_z_mins)} ~ {max(all_z_maxs)} (跨度 {max(all_z_maxs) - min(all_z_mins) + 1} 层)")
            print(f"  单球z跨度: 平均={np.mean(all_z_spans):.1f}层, 最小={min(all_z_spans)}层, 最大={max(all_z_spans)}层")
            print(f"  有效球体数: {len(all_z_mins)}/{len(sphere_masks)}")

    # 6. 保存3D分割结果
    print("\n保存3D分割结果...")
    save_3d_start = time.time()

    # 使用随机背景保存3D分割结果（固定启用）
    output_path = sphere_segmenter.save_3d_segmentation_result_with_rand_bg(volume_data, sphere_masks, "./data/output")
    save_3d_time = time.time() - save_3d_start
    print(f"3D数据保存完成，耗时: {save_3d_time:.2f}秒")

    # 7. 时间统计
    total_time = time.time() - total_start_time
    print(f"\n时间统计:")
    print(f"  数据加载: {data_load_time:.2f}秒")
    print(f"  z精细化: {zref_time:.2f}秒")
    print(f"  3D分割: {segmentation_time:.2f}秒")
    print(f"  数据保存: {save_3d_time:.2f}秒")
    print(f"  总耗时: {total_time:.2f}秒")
    parts_sum = data_load_time + (ai_init_time if model_available else 0) + zref_time + segmentation_time + save_3d_time
    other = total_time - parts_sum
    print(f"  小计: {parts_sum:.2f}秒；其余: {other:.2f}秒")

    print(f"\n球体分割完成")
    if output_path is not None:
        print(f"3D分割原值RAW: {output_path}")
    # if focus_png_path is not None:
    #     print(f"聚焦层分割PNG: {focus_png_path}")

    return {
        'sphere_count': len(sphere_masks),
        'total_time': total_time,
        'output_path': output_path,
        # 'focus_png_path': focus_png_path
    }

if __name__ == '__main__':
    import argparse

    parser = argparse.ArgumentParser(description='WeltDetect - 工业CT小球分割系统')
    parser.add_argument('--input', '-i', type=str, default='./data/processed/Slice_t', help='输入数据路径')
    # parser.add_argument('--input', '-i', type=str, default='./data/processed/defectData_Rec2_png/VT-X750-0997_0000000001_1_00000_20250410005022/Volume_(BGA__4)_0000000006_0000000090/', help='输入数据路径')

    parser.add_argument('--debug', '-d', action='store_true', help='开启调试模式')
    parser.add_argument('--production', '-p', action='store_true', help='生产模式')


    args = parser.parse_args()

    debug_mode = not args.production if args.production else True

    segmentation_params = {}


    print(f"输入数据: {args.input}")
    print(f"运行模式: {'调试' if debug_mode else '生产'}")

    result = main(args.input, debug_mode, **segmentation_params)

    if result:
        print(f"最终结果: 成功分割 {result['sphere_count']} 个球体===============")
