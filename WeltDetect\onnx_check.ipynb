{"cells": [{"cell_type": "code", "execution_count": 8, "id": "445168ab", "metadata": {}, "outputs": [], "source": ["import onnxruntime as ort\n", "import numpy as np\n", "import cv2\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def draw_detections(image, detections, conf_threshold=0.3):\n", "    # 假设detections格式：[num, 6]，其中坐标是像素值格式 cx, cy, w, h, conf, class_id\n", "    for det in detections:\n", "        conf = det[4]\n", "        if conf < conf_threshold:\n", "            continue\n", "\n", "        cx, cy, bw, bh = det[:4]\n", "        class_id = int(det[5])\n", "\n", "        # 坐标是像素值，直接转换成左上角坐标\n", "        left = int(cx - bw / 2)\n", "        top = int(cy - bh / 2)\n", "        width = int(bw)\n", "        height = int(bh)\n", "        print(left, top, width, height)\n", "        # 画框\n", "        cv2.rectangle(image, (left, top), (left + width, top + height), (0, 255, 0), 2)\n", "        label = f\"ID:{class_id} {conf:.2f}\"\n", "        cv2.putText(image, label, (left, max(top - 5, 0)),\n", "                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)\n", "\n", "\n", "def check_onnx_model(model_path, input_image_path=None):\n", "    session = ort.InferenceSession(model_path)\n", "    input_meta = session.get_inputs()[0]\n", "    input_name = input_meta.name\n", "    input_shape = input_meta.shape\n", "    print(f\"Model input shape: {input_shape}\")\n", "\n", "    input_shape = [1, 3, 640, 640]\n", "\n", "    if input_image_path:\n", "        img = cv2.imread(input_image_path, 0)\n", "        if img is None:\n", "            raise RuntimeError(\"Failed to load image from path: \" + input_image_path)\n", "        orig_img = img.copy()\n", "        h, w = img.shape[:2]\n", "        print(f\"Original image size: {w}x{h}\")\n", "\n", "        _, c, h_model, w_model = input_shape\n", "        if h_model is None or w_model is None:\n", "            target_h, target_w = h, w\n", "            print(\"Dynamic input detected, using original image size.\")\n", "        else:\n", "            target_h, target_w = h_model, w_model\n", "            img = cv2.resize(img, (target_w, target_h))\n", "            print(f\"Resized image to {target_w}x{target_h}\")\n", "\n", "        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)\n", "        img = img.astype(np.float32) / 255.0\n", "        img = np.transpose(img, (2, 0, 1))\n", "        input_data = np.expand_dims(img, axis=0)\n", "    else:\n", "        batch = input_shape[0] if input_shape[0] else 1\n", "        channels = input_shape[1] if input_shape[1] else 3\n", "        height = input_shape[2] if input_shape[2] else 640\n", "        width = input_shape[3] if input_shape[3] else 640\n", "        input_data = np.random.randn(batch, channels, height, width).astype(np.float32)\n", "        orig_img = None\n", "        print(f\"Using random input data shape: {input_data.shape}\")\n", "\n", "    outputs = session.run(None, {input_name: input_data})\n", "\n", "    print(f\"Number of outputs: {len(outputs)}\")\n", "    for i, output in enumerate(outputs):\n", "        print(f\"Output[{i}] shape: {output.shape}\")\n", "        print(f\"Output[{i}] sample: {output.flatten()[:10]}\")\n", "\n", "    # 如果有图片，绘制检测结果\n", "    if orig_img is not None:\n", "        # 这里假设第一个输出是检测框，格式需根据你的模型调整\n", "        detections = outputs[0]\n", "\n", "        # 如果输出格式是 (1, N, 6)，先去除batch维度\n", "        if detections.ndim == 3 and detections.shape[0] == 1:\n", "            detections = detections[0]\n", "\n", "        draw_detections(orig_img, detections)\n", "\n", "    return orig_img\n"]}, {"cell_type": "code", "execution_count": 50, "id": "1769342f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model input shape: ['batch', 3, 'height', 'width']\n", "Original image size: 1024x1024\n", "Resized image to 640x640\n", "Number of outputs: 1\n", "Output[0] shape: (1, 25200, 6)\n", "Output[0] sample: [4.1977119e+00 3.5826340e+00 7.0114861e+00 8.1437206e+00 3.3378601e-06\n", " 9.9997771e-01 1.4129606e+01 4.5867910e+00 2.2720644e+01 7.0473747e+00]\n", "249 269 7 7\n", "258 269 7 7\n", "267 269 7 7\n", "276 270 7 7\n", "284 270 7 7\n", "294 270 7 7\n", "303 270 6 7\n", "311 270 7 7\n", "320 270 7 7\n", "329 270 7 7\n", "338 270 6 7\n", "347 270 7 7\n", "356 270 7 7\n", "365 270 7 7\n", "374 271 7 7\n", "383 271 7 7\n", "392 271 7 7\n", "401 271 7 7\n", "249 268 7 16\n", "258 269 16 7\n", "267 270 15 7\n", "276 270 16 7\n", "285 269 16 7\n", "294 269 7 16\n", "303 270 14 7\n", "311 270 7 17\n", "320 270 7 7\n", "329 270 16 7\n", "338 270 15 7\n", "346 270 16 7\n", "356 270 16 7\n", "365 270 7 17\n", "374 271 17 7\n", "383 270 7 17\n", "392 271 7 7\n", "401 271 7 7\n", "249 277 7 17\n", "248 278 16 7\n", "258 278 7 7\n", "276 278 7 7\n", "276 278 7 7\n", "284 287 7 7\n", "293 279 7 7\n", "303 287 7 7\n", "311 279 7 7\n", "320 287 7 7\n", "329 279 7 7\n", "329 279 8 7\n", "347 279 7 7\n", "347 279 7 7\n", "364 279 8 8\n", "365 279 7 7\n", "382 280 8 7\n", "383 279 7 7\n", "400 279 7 7\n", "401 280 7 7\n", "249 287 7 13\n", "249 287 7 7\n", "266 287 7 7\n", "267 287 7 7\n", "284 287 7 7\n", "285 287 7 7\n", "302 287 7 7\n", "302 287 7 7\n", "320 287 8 8\n", "320 287 8 7\n", "338 287 8 8\n", "338 287 8 8\n", "355 288 8 8\n", "355 288 8 8\n", "373 288 8 8\n", "374 288 7 8\n", "391 288 8 7\n", "391 288 7 8\n", "400 288 7 7\n", "400 289 7 7\n", "248 296 7 7\n", "248 296 17 7\n", "257 296 7 7\n", "302 296 7 7\n", "302 296 17 7\n", "311 297 8 8\n", "328 296 8 8\n", "328 297 8 8\n", "347 297 8 8\n", "346 296 8 8\n", "364 297 8 8\n", "364 297 8 8\n", "374 288 7 7\n", "382 297 8 7\n", "391 288 7 8\n", "400 288 7 16\n", "400 297 7 7\n", "248 305 8 7\n", "257 305 7 7\n", "275 305 7 7\n", "275 304 7 8\n", "293 305 8 8\n", "293 305 8 8\n", "302 296 7 7\n", "311 297 7 7\n", "320 306 7 7\n", "328 296 8 8\n", "337 306 7 7\n", "347 297 8 7\n", "355 306 8 7\n", "355 306 8 7\n", "365 297 8 8\n", "373 306 7 7\n", "382 297 8 7\n", "391 306 7 7\n", "400 297 7 16\n", "400 306 7 8\n", "249 305 7 16\n", "257 314 16 7\n", "272 305 10 9\n", "284 314 8 7\n", "284 314 8 7\n", "293 305 8 7\n", "302 314 7 7\n", "320 306 7 16\n", "324 315 12 7\n", "338 306 7 16\n", "337 315 17 7\n", "364 315 7 7\n", "373 306 8 8\n", "382 315 8 7\n", "391 306 7 7\n", "400 306 7 17\n", "400 315 7 7\n", "248 314 7 16\n", "266 314 7 7\n", "275 323 8 8\n", "275 323 8 8\n", "284 314 7 8\n", "293 323 17 7\n", "302 314 7 16\n", "311 323 8 8\n", "320 314 7 7\n", "328 315 7 16\n", "338 315 7 16\n", "347 315 7 8\n", "356 318 7 14\n", "355 322 7 7\n", "364 315 8 7\n", "373 324 8 7\n", "382 315 7 16\n", "391 324 8 8\n", "400 315 7 16\n", "248 322 7 16\n", "257 323 7 7\n", "275 323 8 8\n", "284 331 8 7\n", "284 332 8 7\n", "293 323 8 8\n", "302 323 7 16\n", "311 323 8 8\n", "319 332 7 7\n", "329 323 7 16\n", "338 323 7 16\n", "347 332 7 8\n", "355 324 8 7\n", "364 332 8 8\n", "373 324 15 7\n", "382 324 14 7\n", "391 324 7 7\n", "400 324 7 16\n", "400 324 8 8\n", "248 331 7 16\n", "257 340 7 7\n", "266 341 7 7\n", "284 332 8 8\n", "284 331 7 11\n", "293 341 7 8\n", "302 332 7 16\n", "311 341 7 8\n", "320 332 7 16\n", "337 332 16 7\n", "346 333 8 7\n", "364 332 8 8\n", "364 332 8 8\n", "391 342 7 7\n", "400 333 7 16\n", "400 333 8 8\n", "248 341 7 16\n", "257 341 17 7\n", "266 340 8 8\n", "283 341 8 7\n", "284 341 16 7\n", "293 341 17 7\n", "302 341 7 16\n", "311 341 15 7\n", "319 341 7 16\n", "328 341 7 7\n", "337 341 8 7\n", "355 342 7 7\n", "355 342 8 7\n", "364 350 7 7\n", "382 350 8 8\n", "390 342 7 7\n", "400 342 7 17\n", "400 342 8 8\n", "248 348 7 17\n", "248 349 8 8\n", "266 359 7 7\n", "283 359 8 7\n", "293 358 7 8\n", "302 350 7 7\n", "311 359 7 7\n", "319 350 7 7\n", "328 359 7 7\n", "337 350 7 7\n", "337 350 16 7\n", "346 350 7 7\n", "364 350 7 7\n", "364 351 7 7\n", "382 351 7 7\n", "382 351 7 7\n", "398 354 8 6\n", "399 351 7 7\n", "400 351 7 7\n", "248 358 7 16\n", "247 358 17 7\n", "257 358 16 7\n", "266 358 7 8\n", "283 359 8 8\n", "284 359 16 7\n", "293 359 7 13\n", "310 359 7 7\n", "311 359 7 14\n", "328 359 7 7\n", "328 359 7 7\n", "364 359 7 7\n", "364 359 7 7\n", "382 360 7 7\n", "382 360 16 7\n", "391 360 11 7\n", "400 360 7 7\n", "400 360 7 7\n", "248 367 8 7\n", "248 367 7 7\n", "292 368 7 7\n", "292 368 7 7\n", "310 368 7 7\n", "311 367 7 7\n", "328 368 7 7\n", "337 368 16 7\n", "346 368 16 7\n", "354 368 17 7\n", "364 368 8 7\n", "382 360 7 7\n", "397 361 8 8\n", "399 363 7 13\n", "248 376 7 7\n", "257 376 17 7\n", "266 376 7 7\n", "283 376 8 8\n", "283 376 17 7\n", "292 377 7 7\n", "319 377 7 7\n", "319 377 7 7\n", "328 368 7 7\n", "373 377 7 8\n", "390 377 7 7\n", "390 378 16 7\n", "400 369 7 17\n", "248 377 7 15\n", "257 376 7 8\n", "265 376 7 7\n", "283 376 8 7\n", "293 376 7 7\n", "319 377 7 7\n", "337 377 7 7\n", "346 377 7 7\n", "355 378 7 7\n", "364 377 7 7\n", "372 377 8 8\n", "381 386 8 7\n", "390 378 8 7\n", "399 377 7 17\n", "247 394 7 8\n", "248 385 7 16\n", "256 394 8 7\n", "256 394 8 7\n", "301 395 7 7\n", "301 395 7 7\n", "381 386 8 7\n", "391 395 11 8\n", "399 387 8 16\n", "247 402 7 8\n", "248 394 7 16\n", "256 394 7 16\n", "256 403 7 7\n", "301 403 7 7\n", "345 404 7 7\n", "355 404 7 7\n", "363 404 7 7\n", "364 404 7 7\n", "381 404 8 7\n", "390 395 8 7\n", "399 395 8 17\n", "248 402 7 16\n", "256 403 7 16\n", "265 412 7 7\n", "283 412 7 7\n", "319 413 8 8\n", "336 413 7 7\n", "355 404 17 7\n", "363 404 7 8\n", "372 413 8 8\n", "381 404 8 7\n", "390 413 7 8\n", "399 405 7 16\n", "247 412 7 16\n", "256 412 7 15\n", "267 413 8 10\n", "283 412 7 15\n", "283 412 7 7\n", "292 415 12 8\n", "301 413 7 16\n", "309 421 7 7\n", "319 412 7 17\n", "327 421 7 7\n", "337 413 7 16\n", "345 413 16 7\n", "355 413 8 7\n", "372 413 8 8\n", "372 413 7 17\n", "380 423 8 7\n", "390 413 7 17\n", "399 414 8 16\n", "247 421 7 7\n", "247 421 7 7\n", "256 421 7 7\n", "256 421 16 7\n", "265 421 16 7\n", "274 421 16 7\n", "283 421 15 7\n", "292 421 15 7\n", "302 421 15 7\n", "319 422 9 7\n", "327 421 7 7\n", "336 422 8 7\n", "336 422 16 7\n", "346 422 16 7\n", "355 422 16 7\n", "363 422 17 7\n", "373 422 16 7\n", "381 423 16 7\n", "390 422 16 7\n", "398 422 8 8\n", "249 269 7 7\n", "258 269 7 7\n", "267 269 7 7\n", "284 270 7 7\n", "294 270 7 7\n", "303 270 6 7\n", "311 270 7 7\n", "320 270 7 7\n", "329 270 7 7\n", "339 270 6 7\n", "347 270 7 7\n", "356 270 7 7\n", "365 270 7 7\n", "374 271 7 7\n", "383 271 7 7\n", "392 271 7 7\n", "401 271 7 7\n", "320 270 7 7\n", "338 270 16 7\n", "356 270 16 7\n", "392 271 7 7\n", "401 271 7 7\n", "258 278 7 7\n", "284 287 7 7\n", "293 278 7 7\n", "303 287 7 8\n", "311 278 7 7\n", "320 287 7 7\n", "329 279 7 8\n", "329 279 7 7\n", "347 279 7 7\n", "347 279 7 7\n", "365 279 7 7\n", "365 279 7 7\n", "382 280 8 7\n", "383 279 7 7\n", "401 279 7 7\n", "401 280 7 7\n", "249 287 7 7\n", "267 287 7 7\n", "267 287 7 7\n", "284 287 7 7\n", "285 287 7 7\n", "302 287 7 7\n", "302 287 7 7\n", "320 287 8 7\n", "320 288 8 7\n", "338 287 8 8\n", "338 287 8 8\n", "355 288 8 7\n", "356 288 7 8\n", "373 288 8 8\n", "374 288 7 8\n", "391 288 8 7\n", "391 288 7 7\n", "400 288 7 7\n", "401 289 7 7\n", "249 296 7 7\n", "257 296 7 7\n", "303 296 7 7\n", "302 296 17 7\n", "311 297 8 8\n", "329 296 8 8\n", "328 297 8 8\n", "347 297 8 8\n", "346 296 8 8\n", "364 297 7 8\n", "364 297 8 8\n", "374 288 7 7\n", "382 297 7 7\n", "391 288 7 8\n", "400 297 7 7\n", "248 305 8 7\n", "258 305 7 7\n", "275 305 7 7\n", "275 304 7 8\n", "293 305 7 7\n", "293 305 7 7\n", "302 296 7 7\n", "311 297 7 7\n", "320 306 7 7\n", "329 296 8 8\n", "338 306 7 7\n", "347 296 8 8\n", "355 306 7 7\n", "356 306 8 7\n", "365 297 8 8\n", "373 306 7 7\n", "382 297 8 7\n", "391 306 7 7\n", "400 298 7 16\n", "400 306 7 8\n", "257 314 16 7\n", "272 305 10 8\n", "284 314 7 7\n", "284 314 8 7\n", "293 305 8 8\n", "302 314 7 7\n", "320 306 7 16\n", "338 306 7 16\n", "337 315 17 7\n", "365 315 7 7\n", "373 306 8 7\n", "382 315 8 7\n", "391 306 7 7\n", "400 306 7 17\n", "400 315 7 7\n", "248 314 7 16\n", "266 314 7 7\n", "275 323 8 8\n", "275 323 8 8\n", "284 314 7 8\n", "293 323 16 8\n", "302 314 7 16\n", "311 323 8 8\n", "320 314 7 7\n", "328 315 7 16\n", "338 315 7 16\n", "347 315 7 8\n", "356 318 7 13\n", "364 315 7 7\n", "373 324 7 7\n", "382 315 7 16\n", "391 324 7 7\n", "400 315 7 16\n", "248 322 7 16\n", "257 323 7 7\n", "275 323 8 8\n", "284 332 8 7\n", "293 323 8 8\n", "302 323 7 16\n", "311 323 8 7\n", "319 332 7 7\n", "328 323 7 16\n", "347 332 7 7\n", "355 324 8 7\n", "364 332 8 8\n", "373 324 15 7\n", "383 324 14 8\n", "391 324 7 7\n", "400 324 7 16\n", "400 324 8 8\n", "257 340 7 7\n", "266 341 7 7\n", "284 332 7 8\n", "284 331 7 11\n", "293 341 7 8\n", "302 332 7 16\n", "311 341 7 8\n", "337 332 16 7\n", "346 333 7 7\n", "364 332 8 8\n", "364 332 8 8\n", "391 342 7 7\n", "400 333 7 16\n", "400 333 8 8\n", "248 341 7 16\n", "257 341 17 7\n", "266 340 7 8\n", "283 341 8 7\n", "284 341 16 7\n", "293 341 16 7\n", "302 341 7 16\n", "311 341 14 7\n", "319 342 7 16\n", "328 341 7 8\n", "337 341 7 7\n", "355 342 7 7\n", "355 342 8 7\n", "364 350 7 7\n", "382 350 7 7\n", "390 342 7 7\n", "400 342 7 17\n", "400 341 8 8\n", "248 348 7 16\n", "248 349 8 7\n", "266 359 7 7\n", "283 359 8 7\n", "293 358 7 7\n", "302 350 7 7\n", "311 359 7 7\n", "319 350 7 8\n", "328 359 7 7\n", "337 350 7 7\n", "346 350 7 7\n", "364 350 7 7\n", "364 351 7 8\n", "382 351 7 7\n", "382 351 7 7\n", "398 354 8 7\n", "399 351 7 7\n", "248 358 7 16\n", "257 358 16 7\n", "266 358 7 8\n", "283 359 7 8\n", "284 359 16 7\n", "310 359 7 7\n", "310 359 7 14\n", "328 359 7 7\n", "328 359 7 7\n", "364 359 7 7\n", "364 359 7 7\n", "382 360 7 7\n", "382 360 16 7\n", "391 360 11 7\n", "400 360 7 7\n", "248 367 7 7\n", "248 367 7 7\n", "292 368 7 7\n", "292 368 7 7\n", "346 368 16 7\n", "355 368 16 7\n", "364 368 8 7\n", "397 361 8 8\n", "399 363 8 13\n", "248 376 7 7\n", "266 376 7 7\n", "283 376 8 8\n", "283 376 16 7\n", "292 377 7 7\n", "373 377 7 8\n", "390 378 7 7\n", "391 378 15 7\n", "400 369 7 16\n", "248 377 7 14\n", "257 376 7 8\n", "265 376 7 7\n", "283 376 8 7\n", "292 376 7 7\n", "337 377 7 7\n", "346 377 7 7\n", "355 378 7 7\n", "364 377 7 8\n", "373 377 8 8\n", "381 386 8 7\n", "390 377 8 7\n", "399 377 7 17\n", "248 385 7 16\n", "256 394 8 7\n", "256 394 7 7\n", "301 395 7 7\n", "301 395 7 7\n", "381 386 8 8\n", "391 395 10 8\n", "399 387 8 16\n", "247 402 7 8\n", "248 394 7 15\n", "256 403 7 7\n", "345 404 7 7\n", "355 404 7 7\n", "364 404 7 7\n", "364 404 7 7\n", "381 404 8 7\n", "390 395 8 7\n", "399 395 8 17\n", "248 402 7 16\n", "256 403 7 16\n", "265 412 7 7\n", "283 412 7 7\n", "319 413 8 8\n", "337 413 7 7\n", "363 404 7 8\n", "372 413 8 8\n", "381 404 8 7\n", "390 413 7 8\n", "399 405 7 16\n", "283 412 7 15\n", "283 412 7 7\n", "310 421 7 7\n", "319 412 7 17\n", "327 421 7 7\n", "337 413 7 16\n", "355 413 7 7\n", "372 413 8 8\n", "372 413 7 17\n", "381 423 7 7\n", "390 413 7 17\n", "399 414 8 16\n", "247 421 7 8\n", "247 421 7 7\n", "256 421 7 7\n", "292 421 15 7\n", "319 422 9 7\n", "327 421 7 7\n", "336 422 8 7\n", "346 422 16 7\n", "363 422 16 7\n", "373 422 15 7\n", "381 422 16 7\n", "391 422 15 7\n", "398 422 8 8\n", "320 287 8 7\n", "320 288 8 7\n", "338 287 8 8\n", "355 288 8 7\n", "328 296 8 8\n", "328 297 8 8\n", "347 297 8 8\n", "364 297 8 8\n", "293 305 8 8\n", "328 296 8 8\n", "347 296 8 8\n", "284 314 8 7\n", "284 314 8 7\n", "275 323 8 8\n", "275 323 8 8\n", "283 314 8 8\n", "275 323 8 8\n", "284 332 8 7\n", "364 332 8 8\n", "400 324 8 8\n", "364 332 8 8\n", "364 332 8 8\n", "400 333 8 8\n", "355 342 8 7\n", "400 341 8 8\n", "248 349 8 8\n", "248 367 8 7\n", "283 376 8 7\n", "372 377 8 8\n", "381 386 8 7\n", "256 394 8 7\n", "381 386 8 7\n", "399 395 8 17\n", "398 414 8 16\n", "398 422 8 8\n"]}], "source": ["r = check_onnx_model( model_path=\"/data/lijunlin/project/WeltDetect/models/bestV0.onnx\", input_image_path=\"/data/lijunlin/data/CT/PCB/images/val/0067_12500.442_18014.914.jpg\")"]}, {"cell_type": "code", "execution_count": null, "id": "b38a2d65", "metadata": {}, "outputs": [], "source": ["plt.imshow(r, cmap='gray')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "6e6a73c5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 12, "id": "582d36ed", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "cpp_input = np.fromfile(\"input_tensor_cpp.bin\", dtype=np.float32)\n", "cpp_input_640 = np.fromfile(\"input_tensor_cpp_640.bin\", dtype=np.float32)"]}, {"cell_type": "code", "execution_count": 16, "id": "ffdc4e0b", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(0)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["np.sum(cpp_input != cpp_input_640)"]}, {"cell_type": "code", "execution_count": 13, "id": "75ef7e1f", "metadata": {}, "outputs": [], "source": ["a = cpp_input_640.reshape(640,640,3)"]}, {"cell_type": "code", "execution_count": null, "id": "0e691713", "metadata": {}, "outputs": [], "source": ["plt.imshow(a, cmap='gray')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "a914861e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "det", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}