from PIL import Image
import os
import numpy as np
import argparse
from io import BytesIO

# 超大文件裁剪
from PIL import ImageFile
ImageFile.LOAD_TRUNCATED_IMAGES = True

# 复用与主流程一致的VGI/REC解析与加载逻辑，保证尺寸与位深一致
from src.data_loader import DataLoader


# 简单的路径映射：data/raw/.../name.rec -> data/processed/.../name/
# 其余相对结构保持不变

def _make_loader():
    cfg = {
        'paths': {
            'raw_data': 'data/raw',
            'processed_data': 'data/processed',
        },
        'data_processing': {
            'default_dtype': 'uint16'
        }
    }
    return DataLoader(cfg)


def _to_processed_output_dir(file_path: str, processed_root: str = 'data/processed') -> str:
    file_path = os.path.normpath(file_path)
    raw_root = os.path.normpath(os.path.join('data', 'raw'))
    try:
        rel = os.path.relpath(file_path, raw_root)
        if not rel.startswith('..'):
            parent = os.path.dirname(rel)
            base = os.path.splitext(os.path.basename(file_path))[0]
            return os.path.join(processed_root, parent, base)
    except Exception:
        pass
    base = os.path.splitext(os.path.basename(file_path))[0]
    return os.path.join(processed_root, base)

def extract_partial_pngs_with_missing_signature1(input_path, output_dir = None):
    with open(input_path, 'rb') as f:
        data = f.read()

    ihdr_pattern = b'IHDR'
    iend_pattern = b'IEND'
    png_signature = b'\x89PNG\r\n\x1a\n'

    offset = 0
    count = 0
    png_list = []
    while True:
        # 找到 IHDR 块
        ihdr_pos = data.find(ihdr_pattern, offset)
        if ihdr_pos == -1:
            break

        # PNG 的块格式是：长度(4字节) + 类型(4字节) + 数据 + CRC(4字节)
        # IHDR 前 4 字节是长度，我们往前退 4 字节找到 chunk 起始
        chunk_start = ihdr_pos - 4
        if chunk_start < 0:
            print(f"Invalid IHDR position at {ihdr_pos}")
            break

        # 从 IHDR 开始，向后找 IEND 结束块
        iend_pos = data.find(iend_pattern, ihdr_pos)
        if iend_pos == -1:
            print("IEND not found")
            break
        # IEND 块也是：4字节长度 + 'IEND' + 0字节数据 + 4字节CRC = 12 字节
        chunk_end = iend_pos + 8  # 4字节'IEND' + 4字节CRC

        png_data = png_signature + data[chunk_start:chunk_end]
        if output_dir is not None:
            output_file = os.path.join(output_dir, f"slice_{count:03}.png")
            with open(output_file, 'wb') as out:
                out.write(png_data)
        uint8_array = np.frombuffer(png_data, dtype=np.uint8)
        png_list.append( uint8_array )
        print(f"Extracted PNG slice {count} from offset {chunk_start} to {chunk_end}")
        offset = chunk_end
        count += 1

    print(f"✅ 提取完毕，共提取 {count} 张图像")
    return png_list

def extract_partial_pngs_with_missing_signature(input_path, output_dir):
    with open(input_path, 'rb') as f:
        data = f.read()
    os.makedirs(output_dir, exist_ok=True)
    ihdr_pattern = b'IHDR'
    iend_pattern = b'IEND'
    png_signature = b'\x89PNG\r\n\x1a\n'

    offset = 0
    count = 0
    while True:
        # 找到 IHDR 块
        ihdr_pos = data.find(ihdr_pattern, offset)
        if ihdr_pos == -1:
            break

        # PNG 的块格式是：长度(4字节) + 类型(4字节) + 数据 + CRC(4字节)
        # IHDR 前 4 字节是长度，我们往前退 4 字节找到 chunk 起始
        chunk_start = ihdr_pos - 4
        if chunk_start < 0:
            print(f"Invalid IHDR position at {ihdr_pos}")
            break

        # 从 IHDR 开始，向后找 IEND 结束块
        iend_pos = data.find(iend_pattern, ihdr_pos)
        if iend_pos == -1:
            print("IEND not found")
            break
        # IEND 块也是：4字节长度 + 'IEND' + 0字节数据 + 4字节CRC = 12 字节
        chunk_end = iend_pos + 8  # 4字节'IEND' + 4字节CRC

        png_data = png_signature + data[chunk_start:chunk_end]
        output_file = os.path.join(output_dir, f"slice_long_{count:03}.png")
        with open(output_file, 'wb') as out:
            out.write(png_data)

        print(f"Extracted PNG slice {count} from offset {chunk_start} to {chunk_end}")
        offset = chunk_end
        count += 1

    print(f"✅ 提取完毕，共提取 {count} 张图像")

def split_long_png_into_slices(png_path, output_dir, slice_height=536):
    os.makedirs(output_dir, exist_ok=True)
    img = Image.open(png_path)
    width, height = img.size

    num_slices = height // slice_height
    print(f"{png_path} , 图像尺寸：{width}x{height}，将分割为 {num_slices} 张切片")
    slice_list = []
    for i in range(num_slices):
        box = (0, i * slice_height, width, (i + 1) * slice_height)
        slice_img = img.crop(box)
        slice_img.save(os.path.join(output_dir, f"slice_{i:03}.png"))
        slice_list.append(img)

    print("✅ 分割完成")

# 解析 .vgi，返回体数据的基本信息
def parse_vgi(vgi_path):
    info = {
        'width': None,
        'height': None,
        'slices': None,
        'bits': None,
        'endian': 'little'
    }
    if not os.path.exists(vgi_path):
        raise FileNotFoundError(f"vgi不存在: {vgi_path}")
    with open(vgi_path, 'r', encoding='utf-8', errors='ignore') as f:
        lines = f.readlines()
    def split_kv(line):
        if '=' in line:
            k, v = line.split('=', 1)
        elif ':' in line:
            k, v = line.split(':', 1)
        else:
            return None, None
        return k.strip().lower(), v.strip()
    for raw in lines:
        line = raw.strip()
        if not line or line.startswith('#') or line.startswith('//'):
            continue
        k, v = split_kv(line)
        if not k:
            continue
        # 尺寸
        if any(key in k for key in ['resolution', 'dimsize', 'dimensions', 'volumesize', 'size']):
            nums = [int(x) for x in v.replace(',', ' ').split() if x.isdigit()]
            if len(nums) >= 3:
                info['width'], info['height'], info['slices'] = nums[0], nums[1], nums[2]
                continue
        if 'x' in k and 'dimension' in k:
            try:
                info['width'] = int(v)
            except:
                pass
        if 'y' in k and 'dimension' in k:
            try:
                info['height'] = int(v)
            except:
                pass
        if ('z' in k and 'dimension' in k) or ('slices' in k):
            try:
                info['slices'] = int(v)
            except:
                pass
        # 位深
        if any(key in k for key in ['bits', 'bitdepth', 'bitsperpixel']):
            try:
                info['bits'] = int(''.join(ch for ch in v if ch.isdigit()))
            except:
                pass
        if 'datatype' in k:
            vv = v.lower()
            if '16' in vv or 'short' in vv:
                info['bits'] = 16
            elif '8' in vv or 'byte' in vv:
                info['bits'] = 8
        # 大小端
        if any(key in k for key in ['endian', 'endianess', 'byteorder', 'littleendian']):
            vv = v.lower()
            if 'big' in vv:
                info['endian'] = 'big'
            elif 'little' in vv or '1' == vv:
                info['endian'] = 'little'
    if not all([info['width'], info['height'], info['slices']]):
        raise ValueError(f"vgi缺少尺寸信息: {vgi_path}")
    if info['bits'] is None:
        info['bits'] = 16
    return info

# 将 .rec 转为逐层 PNG（按 data_loader 的解析方式，确保尺寸与位深一致）
def convert_rec_to_pngs(rec_path, output_dir=None):
    if not os.path.exists(rec_path):
        raise FileNotFoundError(rec_path)

    loader = _make_loader()
    volume = loader.load_volume_from_rec(rec_path)  # shape: (Z, H, W)

    # 输出目录：默认将 data/raw/.../name.rec 映射为 data/processed/.../name/
    if output_dir is None:
        output_dir = _to_processed_output_dir(rec_path, processed_root='data/processed')

    os.makedirs(output_dir, exist_ok=True)

    Z = volume.shape[0]
    is_u16 = (volume.dtype == np.uint16)

    for z in range(Z):
        sl = volume[z]
        if is_u16:
            img = Image.fromarray(sl, mode='I;16')
        else:
            # 若不是8位，做安全转换
            img = Image.fromarray(sl.astype(np.uint8) if sl.dtype != np.uint8 else sl, mode='L')
        img.save(os.path.join(output_dir, f'slice_{z:03}.png'))
    print(f"✅ 转换完成: {rec_path} -> {output_dir} ({Z} 张)")
    return output_dir

import argparse
from io import BytesIO

def process_rec2_to_pngs(rec2_path, output_dir, slice_height=536):
    os.makedirs(output_dir, exist_ok=True)
    png_arrays = extract_partial_pngs_with_missing_signature1(rec2_path, output_dir=None)
    count = 0
    for arr in png_arrays:
        img = Image.open(BytesIO(arr.tobytes()))
        w, h = img.size
        num_slices = h // slice_height
        for i in range(num_slices):
            box = (0, i * slice_height, w, (i + 1) * slice_height)
            sl = img.crop(box)
            sl.save(os.path.join(output_dir, f"slice_{count:03}.png"))
            count += 1
    print(f"✅ 转换完成: {rec2_path} -> {output_dir} ({count} 张)")
    return output_dir

def scan_and_convert(input_path, processed_root="data/processed", slice_height=536):
    paths = []
    if os.path.isdir(input_path):
        for root, dirs, files in os.walk(input_path):
            for f in files:
                if f.lower().endswith(('.rec', '.rec2')):
                    paths.append(os.path.join(root, f))
    else:
        if input_path.lower().endswith(('.rec', '.rec2')):
            paths.append(input_path)
    if not paths:
        print("未找到 rec/rec2 文件")
        return
    os.makedirs(processed_root, exist_ok=True)
    for p in paths:
        base = os.path.splitext(os.path.basename(p))[0]
        out_dir = os.path.join(processed_root, base)
        if p.lower().endswith('.rec'):
            try:
                convert_rec_to_pngs(p, out_dir)
            except Exception as e:
                print(f"处理失败: {p}: {e}")
        else:
            try:
                process_rec2_to_pngs(p, out_dir, slice_height=slice_height)
            except Exception as e:
                print(f"处理失败: {p}: {e}")


def main():
    parser = argparse.ArgumentParser(description="将 .rec/.rec2 转为逐层 PNG")
    parser.add_argument('--input', required=True, help='文件或目录')
    parser.add_argument('--processed-root', default='data/processed', help='输出根目录')
    parser.add_argument('--slice-height', type=int, default=536, help='rec2长图每层高度')
    args = parser.parse_args()

    scan_and_convert(args.input, processed_root=args.processed_root, slice_height=args.slice_height)

if __name__ == '__main__':
    main()
