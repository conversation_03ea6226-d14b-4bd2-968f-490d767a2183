import matplotlib.pyplot as plt
from typing import List, Tuple
import numpy as np
import os
import cv2
import time
from src.ai_inference import *
from concurrent.futures import ThreadPoolExecutor
import multiprocessing

class AutoFocusProcessor:
    """
    自动对焦处理器
    """
    def __init__(self, config: dict, debug_mode = False):
        self.config = config
        self.grid_size = config['auto_focus']['grid_size']
        self.peak_range_ratio = config['auto_focus']['peak_range_ratio']
        self.use_parallel = config['auto_focus'].get('use_parallel', True)
        self.max_workers = config['auto_focus'].get('max_workers', min(8, multiprocessing.cpu_count()))

        # 批量推理配置
        batch_config = config['auto_focus'].get('batch_inference', {})
        self.search_range = batch_config.get('search_range', 8)
        self.layer_interval = batch_config.get('layer_interval', 3)
        self.min_thickness = batch_config.get('min_thickness', 8)

        self.debug_mode = debug_mode

    def score_tenengrad(self, image):
        Gx = cv2.Sobel(image, cv2.CV_64F, 1, 0, ksize=3)
        Gy = cv2.Sobel(image, cv2.CV_64F, 0, 1, ksize=3)
        return np.mean(Gx**2 + Gy**2)

    def score_laplacian(self, image):
        return cv2.Laplacian(image, cv2.CV_64F).var()

    def cal_score(self, tile):
        """
        计算 tile 的得分，这里简单的计算 tile 的均值
        """
        # 转换为 8bit 再计算 （更稳定）
        if tile.dtype != np.uint8:
            norm_slice = ((tile - tile.min()) / (tile.max() - tile.min()) * 255).astype(np.uint8)
        else:
            norm_slice = tile
        focus_measure = self.score_tenengrad(norm_slice)

        return focus_measure

    def cal_all_slice_score(self, volume):
        """向量化计算所有层的聚焦评分"""
        num_layers = volume.shape[0]
        focus_scores = np.zeros(num_layers)

        # 预处理：统一归一化整个volume
        if volume.dtype != np.uint8:
            volume_min = volume.min()
            volume_max = volume.max()
            if volume_max > volume_min:
                volume_norm = ((volume - volume_min) / (volume_max - volume_min) * 255).astype(np.uint8)
            else:
                volume_norm = np.zeros_like(volume, dtype=np.uint8)
        else:
            volume_norm = volume

        # 向量化计算所有层的Tenengrad评分
        for i in range(num_layers):
            slice_img = volume_norm[i]
            focus_scores[i] = self.score_tenengrad(slice_img)

        best_index = int(np.argmax(focus_scores))
        return best_index, focus_scores.tolist()

    def cal_all_slice_score_parallel(self, volume):
        """并行计算所有层的聚焦评分"""
        num_layers = volume.shape[0]

        # 预处理：统一归一化整个volume
        if volume.dtype != np.uint8:
            volume_min = volume.min()
            volume_max = volume.max()
            if volume_max > volume_min:
                volume_norm = ((volume - volume_min) / (volume_max - volume_min) * 255).astype(np.uint8)
            else:
                volume_norm = np.zeros_like(volume, dtype=np.uint8)
        else:
            volume_norm = volume

        def compute_layer_score(layer_idx):
            return self.score_tenengrad(volume_norm[layer_idx])

        # 并行计算所有层的评分
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            focus_scores = list(executor.map(compute_layer_score, range(num_layers)))

        best_index = int(np.argmax(focus_scores))
        return best_index, focus_scores

    def AMPD(self, data):
        """
        优化后的AMPD算法，消除冗余计算
        改进点：
        1. 使用候选峰值机制，避免重复检测非峰值位置
        2. 仅在可能成为真正峰值的位置进行后续检测
        3. 动态缩减检测范围，提高计算效率

        :param data: 1-D numpy.ndarray
        :return: 波峰所在索引值的列表
        """
        data = np.array(data)
        n = data.shape[0]

        # 处理小数据情况
        if n < 3:
            return np.array([], dtype=int)

        # 第一步：确定最佳窗口长度
        max_k = n // 2
        arr_rowsum = np.zeros(max_k, dtype=int)

        # 向量化计算每个k的峰值计数
        for k in range(1, max_k + 1):
            # 创建窗口视图
            left = data[:-2*k]      # [0, n-2k-1]
            middle = data[k:n-k]    # [k, n-k-1]
            right = data[2*k:]      # [2k, n-1]

            # 向量化比较并计数
            count_peak = np.sum((middle > left) & (middle > right))
            arr_rowsum[k-1] = -count_peak  # 保持原逻辑（负计数）

        # print(arr_rowsum)
        # a_list = []
        # for i in range(len(arr_rowsum)):
        #     if arr_rowsum[i] != 0:
        #         a_list.append(i)
        # print(a_list)
        min_index = np.argmin(arr_rowsum)
        max_window_length = min_index + 1

        # 第二步：使用候选峰值机制进行高效检测
        # 初始候选集：所有可能成为峰值的位置
        candidate_mask = np.ones(n, dtype=bool)

        # 初始化峰值计数
        p_data = np.zeros(n, dtype=np.int32)

        # 从最小尺度开始逐步筛选
        for k in range(1, max_window_length + 1):
            # 只检测当前候选位置
            valid_indices = np.where(candidate_mask[k:n-k])[0] + k

            if len(valid_indices) == 0:
                break

            # 获取当前k值的检测窗口
            left_vals = data[valid_indices - k]
            middle_vals = data[valid_indices]
            right_vals = data[valid_indices + k]

            # 检测当前k值下的峰值
            is_peak = (middle_vals > left_vals) & (middle_vals > right_vals)

            # 更新峰值计数
            p_data[valid_indices] += is_peak

            # 更新候选掩码：
            # 1. 当前不是峰值的点移除候选集
            # 2. 同时考虑边界变化（较大k值需要更大边界）
            candidate_mask[valid_indices[~is_peak]] = False

            # 更新边界：下个k值需要更大的边界空间
            candidate_mask[:k+1] = False
            candidate_mask[n-k-1:] = False

        # 返回所有满足条件的峰值位置
        return np.where(p_data == max_window_length)[0]
    def auto_focus(self, volume_data, thea = 0.75):
        # 根据配置选择串行或并行计算
        if self.use_parallel and volume_data.shape[0] > 50:  # 层数较多时使用并行
            best_idx, scores = self.cal_all_slice_score_parallel(volume_data)
        else:
            best_idx, scores = self.cal_all_slice_score(volume_data)



        peak_idx_list = self.AMPD(scores)

        # 2. 检查首层
        if scores[0] >= np.max(scores):
            peak_idx_list = np.append(peak_idx_list, 0)

        # 3. 检查末层
        if scores[-1] >= np.max(scores):
            peak_idx_list = np.append(peak_idx_list, len(scores) - 1)

        peak_idx_list.sort()
        return peak_idx_list, best_idx, scores


    def select_best_tiles(self, data, grid_size):
        """
        对每一层划分为 grid_size × grid_size 的网格，并在每个网格位置选择最优层。

        参数:
            data: 输入数据，形状为 (height, width, num_layers)
            grid_size: 网格大小（如 3 表示 3×3 网格）
            cal_score: 计算得分的函数，输入是 tile，输出是得分

        返回:
            拼接后的图像，形状为 (height, width)，每个像素来自最优层
        """
        num_layers, height, width = data.shape

        # 计算网格边界（支持任意尺寸，最后一格自适应）
        h_edges = np.linspace(0, height, grid_size + 1, dtype=int)
        w_edges = np.linspace(0, width, grid_size + 1, dtype=int)

        # 初始化输出图像
        output = np.zeros((height, width), dtype=data.dtype if hasattr(data, 'dtype') else np.float32)
        best_indices = np.zeros((grid_size, grid_size), dtype=np.int32)
        score_indices = np.zeros((grid_size, grid_size), dtype=np.int32)

        # 遍历每个网格位置
        for i in range(grid_size):
            for j in range(grid_size):
                h_start, h_end = h_edges[i], h_edges[i + 1]
                w_start, w_end = w_edges[j], w_edges[j + 1]
                if h_end <= h_start or w_end <= w_start:
                    continue

                best_score = -np.inf
                best_layer_idx = 0

                # 优化：批量计算所有层的评分，减少重复的数据预处理
                tile_scores = np.zeros(num_layers)

                # 提取当前网格区域的所有层数据
                grid_data = data[:, h_start:h_end, w_start:w_end]

                # 统一预处理这个网格区域的数据
                if grid_data.dtype != np.uint8:
                    grid_min = grid_data.min()
                    grid_max = grid_data.max()
                    if grid_max > grid_min:
                        grid_norm = ((grid_data - grid_min) / (grid_max - grid_min) * 255).astype(np.uint8)
                    else:
                        grid_norm = np.zeros_like(grid_data, dtype=np.uint8)
                else:
                    grid_norm = grid_data

                # 批量计算所有层的Tenengrad评分
                for layer_idx in range(num_layers):
                    tile_scores[layer_idx] = self.score_tenengrad(grid_norm[layer_idx])

                # 找到最优层
                best_layer_idx = int(np.argmax(tile_scores))
                best_score = tile_scores[best_layer_idx]

                # 将最优层的 tile 放入输出
                output[h_start:h_end, w_start:w_end] = data[best_layer_idx, h_start:h_end, w_start:w_end]
                best_indices[i, j] = best_layer_idx + 1
                score_indices[i, j] = int(best_score) if np.isfinite(best_score) else 0

        return output, best_indices, score_indices

    def generate_best_slice(self, volume_data):
        '''
        1.找到聚焦层峰值
        2.针对每个聚焦层将上下范围内的层找tile块内最优
        '''
        focus_list = []
        layer_number, width, height = volume_data.shape

        # ✅ 修复点：确保 MIN_PEAK_RANGE 至少为 1
        MIN_PEAK_RANGE = max(int(layer_number * self.peak_range_ratio), 1)

        peak_idx_list, global_best_idx, all_scores = self.auto_focus(volume_data)  # 计算全局总得分
        peak_idx_list = np.append(peak_idx_list, layer_number - 1)
        # print(all_scores)


        for i in range(len(peak_idx_list[:-1])):
            peak_idx = peak_idx_list[i]
            START_IDX = max( peak_idx - MIN_PEAK_RANGE, 0)
            END_IDX   = min( peak_idx + MIN_PEAK_RANGE, peak_idx_list[i+1])

            # ✅ 修复点：确保 END_IDX > START_IDX，否则直接跳过或强制扩展范围
            if END_IDX <= START_IDX:
                END_IDX = min( START_IDX + 1, layer_number)

            data = volume_data[ START_IDX : END_IDX, :, :]
            if data.shape[0] == 0:  # 再次安全检查
                continue

            focus_slice, idx, score = self.select_best_tiles(data, self.grid_size)  # 计算范围内局部得分
            focus_list.append(focus_slice)
            print("idx:\n", idx + START_IDX, "\nscore:\n", score)

        if len( focus_list) == 0:
            return [volume_data[global_best_idx]], [global_best_idx],  all_scores
        else:
            return focus_list, peak_idx_list[:-1], all_scores


    def generate_best_slice_ai(self, volume_data, inference = None):
        '''
        1.找到聚焦层峰值
        2.针对每个聚焦层将上下范围内的层找tile块内最优
        '''
        global_slice = []
        peak_slice_list = []
        z_func = []


        layer_number, height, width  = volume_data.shape

        # ✅ 修复点：确保 MIN_PEAK_RANGE 至少为 1
        MIN_PEAK_RANGE = max(int(layer_number * 0.1), 15)
        GAP = 2
        peak_idx_list, global_best_idx, all_scores = self.auto_focus(volume_data)  # 计算全局总得分

        peak_idx_list = np.append(peak_idx_list, layer_number - 1)
        # print(all_scores)

        for idx_i in range(len(peak_idx_list[:-1])):
            peak_idx = peak_idx_list[idx_i]
            START_IDX = max( peak_idx - MIN_PEAK_RANGE, 0)
            END_IDX   = min( peak_idx + MIN_PEAK_RANGE, peak_idx_list[idx_i+1])

            # ✅ 修复点：确保 END_IDX > START_IDX，否则直接跳过或强制扩展范围
            if END_IDX <= START_IDX:
                END_IDX = min( START_IDX + 1, layer_number)

            data = volume_data[ START_IDX  : END_IDX : GAP, :, :]
            if self.debug_mode:
                print(f"start_idx: {START_IDX}, end_idx: {END_IDX}, GAP: {GAP}")

            if data.shape[0] == 0:  # 再次安全检查
                continue

            result_l = inference( data)

            total_result = []
            count = 0
            col = []
            for slice_detection in result_l:
                z = count * GAP + START_IDX
                if slice_detection is not None:
                    for obj in slice_detection:
                        if obj[-1] == 3:
                            continue
                        total_result.append( np.hstack([ obj[ :4], z , obj[ 4:] ])  )

                count += 1

            result = np.array(total_result)
            if result.shape[0] == 0:
                continue

            extreme_rects = find_corner_samples(result, height, width)
            idx, predicted_rect = find_outlier_and_predict_fourth(np.array(extreme_rects))

            if self.debug_mode:
                print("corner samples:\n")
                for coord in extreme_rects:
                    print(coord)
                print("pop", idx)

            extreme_rects.pop(idx)


            data = volume_data[ START_IDX : END_IDX , :, :]

            for d in extreme_rects:
                x1,y1,x2,y2 = map(int, d[:4])
                w1 = x2 - x1
                h1 = y2 - y1
                best_z = -1
                best_slice = -1

                for i_z in range(len(data)):
                    score = self.cal_score(
                        data[ i_z,
                            max(y1 - 2 * h1, 0) : min(y2 + 2 * h1, height),
                            max(x1 - 2 * w1, 0) : min(x2 + 2 * w1, width)
                        ]
                    )
                    if score > best_z:
                        best_z = score
                        best_slice = i_z
                d[4] = best_slice + START_IDX

            pts = []
            for coord in extreme_rects:

                cx = (coord[0] + coord[2])/2
                cy = (coord[1] + coord[3])/2
                bestz = coord[4]
                pts.append( (cx,cy,bestz) )


            a,b,c = _fit_plane(pts)

            if self.debug_mode:
                print(f"拟合平面 {peak_idx}±{MIN_PEAK_RANGE}: z = {a:.4f} x + {b:.4f} y + {c:.2f}")
            z_func.append( [a, b, c])
            best_slice = sample_plane_from_volume( volume_data, a, b, c)
            global_slice.append( best_slice)
            peak_slice_list.append(peak_idx)



    def detect_balls_precise(self, volume_data: np.ndarray, infer):
        """
        一体化检测：输入体数据，输出带精确z的检测框与复合图。
        步骤：
          1) 目标层选择：先自动对焦得到若干峰层；若有多个峰，则在每个峰层跑一次AI，取检测数量最多的层为唯一目标层。
          2) 稳健建模：以目标层为中心，按间隔2层在±7层范围内抽样检测（批量推理）；汇总所有检测（忽略类3），选四角0类框为代表；
             对每个代表框扩大为2倍边，围绕当前z在±7层内用Tenengrad精搜最优z，得到四角(cx,cy,z)三元组；拟合平面z=ax+by+c。
          3) 逐像素拉平：使用平面参数从volume采样得到复合图；在复合图上跑AI得到最终检测，
             对每个检测框中心代入平面函数计算精确z并写入；同时输出复合图。
        返回：roi_box(rx0,ry0,rx1,ry1), composite(np.ndarray[h,w]), detections(ndarray[N,7])。
        """
        import time
        Z, H, W = volume_data.shape
        t0 = time.time()

        # 1) 目标层选择（多峰中选检测最多的一层）
        t_peak0 = time.time()
        peak_idx_list, global_best_idx, _ = self.auto_focus(volume_data)
        if peak_idx_list.size == 0:
            peak_idx_list = np.array([global_best_idx], dtype=int)

        print(f"目标层选择详情:")
        print(f"  所有候选层: {list(peak_idx_list)}")
        print(f"  聚焦度最优层: {global_best_idx}")

        best_layer = int(global_best_idx)
        best_count = -1
        print(f"  多峰推理比较:")
        for zc in peak_idx_list:
            res = infer.inference(volume_data[int(zc)])
            cnt = len(res[0]) if res and len(res) > 0 and res[0] is not None else 0
            print(f"    z={zc}: 检测数={cnt}")
            if cnt > best_count:
                best_count = cnt
                best_layer = int(zc)

        print(f"  最终选择层: z={best_layer} (检测数={best_count})")
        t_peak1 = time.time()
        print(f"zref-计时: 目标层选择 {t_peak1 - t_peak0:.3f}s (峰数={len(peak_idx_list)})")

        # ========== 2) 多层稳健检测与角点精搜 ==========
        print(f"\n【多层检测与角点精搜】")
        t_ml0 = time.time()

        # 使用配置参数确定搜索范围和间隔
        search_range = max(self.search_range, self.min_thickness // 2)  # 确保满足最小厚度要求
        START_IDX = max(0, best_layer - search_range)
        END_IDX = min(Z - 1, best_layer + search_range)
        sampled_indices = list(range(START_IDX, END_IDX + 1, self.layer_interval))

        # 确保目标层本身被包含
        if best_layer not in sampled_indices:
            sampled_indices.append(best_layer)
            sampled_indices.sort()

        print(f"  配置参数: 搜索范围±{search_range}层, 间隔{self.layer_interval}层, 总厚度{END_IDX-START_IDX+1}层")

        if len(sampled_indices) > 0:
            t_batch_prep = time.time()
            batch = np.stack([volume_data[zi] for zi in sampled_indices], axis=0)
            batch_prep_time = time.time() - t_batch_prep

            tb0 = time.time()
            batch_res = infer.inference_batch(batch)
            tb1 = time.time()
            batch_infer_time = tb1 - tb0

            print(f"    批量数据准备: {batch_prep_time:.3f}s")
            print(f"    批量推理执行: {batch_infer_time:.3f}s (处理{len(sampled_indices)}层)")
        else:
            batch_res = []

        t_process_start = time.time()
        agg = []  # [x1,y1,x2,y2,z,conf,cls]
        for zi, res in zip(sampled_indices, batch_res):
            if res is None or len(res) == 0:
                continue

            # res 是单层的检测结果数组
            detections = res
            if detections is None or len(detections) == 0:
                continue

            for row in detections:
                try:
                    if hasattr(row, 'tolist'):
                        row_data = row.tolist()[:6]
                    else:
                        # row might already be a list or tuple
                        row_data = list(row)[:6]

                    if len(row_data) < 6:
                        continue

                    x1, y1, x2, y2, conf, cls = row_data
                    if int(cls) == 3:
                        continue
                    agg.append([x1, y1, x2, y2, zi, conf, int(cls)])
                except (TypeError, ValueError, IndexError) as e:
                    print(f"Warning: Failed to process detection row: {row}, error: {e}")
                    continue
        if len(agg) == 0:
            return (0,0,W,H), np.zeros((0,0), dtype=volume_data.dtype), np.zeros((0, 7), dtype=np.float32)

        t_process_end = time.time()
        process_time = t_process_end - t_process_start
        print(f"    检测结果汇总: {process_time:.3f}s (有效检测={len(agg)}个)")

        print(f"\n  【角点选择与精搜】")
        agg_np = np.array(agg, dtype=np.float32)
        corners = find_corner_samples(agg_np, H, W)
        # 基于相邻性的角点选择算法已经选出符合方形要求的角点，无需再去除离群点
        print(f"    最终角点数量: {len(corners)}个")

        # 基于四角代表框计算最小外接框，作为后续裁剪范围
        t_roi_start = time.time()
        xs1_c = [int(d[0]) for d in corners]
        ys1_c = [int(d[1]) for d in corners]
        xs2_c = [int(d[2]) for d in corners]
        ys2_c = [int(d[3]) for d in corners]
        rx0 = max(0, min(xs1_c)); ry0 = max(0, min(ys1_c))
        rx1 = min(W, max(xs2_c)); ry1 = min(H, max(ys2_c))
        if rx1 <= rx0 or ry1 <= ry0:
            rx0, ry0, rx1, ry1 = 0, 0, W, H
        t_roi_end = time.time()
        roi_time = t_roi_end - t_roi_start
        print(f"  - ROI区域计算: {roi_time:.3f}s (区域={rx1-rx0}×{ry1-ry0})")

        # 四角框逐个精搜z
        refined_pts = []  # (cx,cy,z)
        # 快速聚焦打分：下采样 + Laplacian 方差；较 Tenengrad 更快
        def _fast_focus_score(img):
            h, w = img.shape
            if h > 128 and w > 128:
                img = cv2.resize(img, (w//2, h//2), interpolation=cv2.INTER_AREA)
            lap = cv2.Laplacian(img, cv2.CV_32F, ksize=3)
            return float(lap.var())

        tc_ref0 = time.time()
        print(f"    四角精搜详情:")
        for i, det in enumerate(corners):
            x1, y1, x2, y2, zc = map(int, det[:5])
            cx, cy = (x1 + x2) / 2.0, (y1 + y2) / 2.0
            print(f"      角点{i+1}: 框[{x1},{y1},{x2},{y2}], 中心({cx:.1f},{cy:.1f}), 初始z={zc}")

        for det in corners:
            x1, y1, x2, y2, zc = map(int, det[:5])
            w1 = max(1, int(x2 - x1))
            h1 = max(1, int(y2 - y1))
            cx = (x1 + x2) / 2.0
            cy = (y1 + y2) / 2.0

            z0 = max(0, zc - 10)  # 扩大搜索范围
            z1 = min(Z - 1, zc + 10)

            best_z = zc
            best_score = -1.0
            x0 = max(0, int(cx - 2 * w1))
            y0 = max(0, int(cy - 2 * h1))
            x1r = min(W, int(cx + 2 * w1))
            y1r = min(H, int(cy + 2 * h1))
            for zi in range(z0, z1 + 1):
                tile = volume_data[zi, y0:y1r, x0:x1r]
                if tile.size == 0:
                    continue
                score = _fast_focus_score(tile)
                if score > best_score:
                    best_score = score
                    best_z = zi

            print(f"  角点({cx:.1f},{cy:.1f}): 原始z={zc}, 搜索范围[{z0}:{z1}], 精确z={best_z}, 偏移={best_z-zc}")
            refined_pts.append((cx, cy, best_z))
        tc_ref1 = time.time()
        refine_time = tc_ref1 - tc_ref0
        print(f"      四角精搜总耗时: {refine_time:.3f}s")

        # ========== 3) 平面拟合与复合图生成 ==========
        print(f"\n  【平面拟合与复合图生成】")
        tf0 = time.time()
        a, b, c = _fit_plane(refined_pts if len(refined_pts) >= 3 else [(W/2, H/2, best_layer)])
        tf1 = time.time()
        fit_time = tf1 - tf0
        print(f"    平面拟合计算: {fit_time:.3f}s")
        print(f"    拟合函数: z = {a:.6f}*x + {b:.6f}*y + {c:.3f}")

        tflat0 = time.time()
        composite = sample_plane_from_volume_roi(volume_data, a, b, c, rx0, ry0, rx1, ry1)
        tflat1 = time.time()
        flatten_time = tflat1 - tflat0
        print(f"    逐像素拉平: {flatten_time:.3f}s (复合图尺寸={composite.shape})")

        # ========== 4) 复合图最终检测 ==========
        print(f"\n  【复合图最终检测】")
        tinf0 = time.time()
        final_res = infer.inference(composite)
        tinf1 = time.time()
        composite_infer_time = tinf1 - tinf0
        roi_area = (rx1 - rx0) * (ry1 - ry0)
        original_area = H * W
        reduction_ratio = roi_area / original_area
        print(f"    推理执行: {composite_infer_time:.3f}s (ROI缩减比例={reduction_ratio:.3f})")
        t_result_start = time.time()
        final = []
        z_values = []
        if final_res and len(final_res) > 0 and final_res[0] is not None:
            for row in final_res[0]:
                x1, y1, x2, y2, conf, cls = row.tolist()[:6]
                cx = (x1 + x2) / 2.0
                cy = (y1 + y2) / 2.0
                z_est = a * (cx + rx0) + b * (cy + ry0) + c  # 将局部坐标还原回全局以计算z
                zi = int(np.clip(np.round(z_est), 0, Z - 1))
                final.append([x1, y1, x2, y2, zi, conf, int(cls)])
                z_values.append(zi)

        t_result_end = time.time()
        result_time = t_result_end - t_result_start
        print(f"  - 结果坐标转换: {result_time:.3f}s (检测数={len(final)})")

        # 打印z统计
        if len(z_values) > 0:
            print("所有检测框的z层号统计：")
            print(f"  z层号范围: {min(z_values)} ~ {max(z_values)} (跨度 {max(z_values)-min(z_values)+1} 层)")
            print(f"  z层号分布: {z_values}")

        dets = np.array(final, dtype=np.float32) if len(final) > 0 else np.zeros((0, 7), dtype=np.float32)

        # 在复合图上绘制检测结果
        try:
            img0 = composite.copy()
            if len(img0.shape) == 2:

                img_vis = cv2.cvtColor((img0 / (img0.max() if img0.max()>0 else 1) * 255).astype(np.uint8), cv2.COLOR_GRAY2BGR)
            else:
                img_vis = img0
            def color_for_class(cid:int):
                return {0:(0,0,255), 1:(0,255,0), 2:(255,0,0), 3:(0,255,255)}.get(cid,(255,255,255))
            class_counts = {}
            for det in dets:
                x1,y1,x2,y2,zi,conf,cid = det.astype(np.float32).tolist()
                x1=int(x1);y1=int(y1);x2=int(x2);y2=int(y2);cid=int(cid)
                class_counts[cid] = class_counts.get(cid,0)+1
                img_vis = cv2.rectangle(img_vis, (x1,y1), (x2,y2), color_for_class(cid), 1)
            print("检测类别统计（类别 -> 颜色/数量）：")
            for cid in sorted(class_counts.keys()):
                print(f"  类{cid} 颜色(B,G,R)={color_for_class(cid)} 数量={class_counts[cid]}")
            save_path = "./data/output/detections_on_composite.png"
            cv2.imwrite(save_path, img_vis)
            print(f"detect save: {save_path}")
        except Exception as e:
            print(f"复合图可视化失败: {e}")

        # z精细化总时间统计在main.py中进行

        # 返回最小外接框、局部复合图与局部坐标系的检测框
        roi_box = (rx0, ry0, rx1, ry1)
        return roi_box, composite, dets

def sample_plane_from_volume(volume_data, a, b, c):
    """
    volume_data: 3D ndarray, shape (D, H, W)
    a, b, c: 平面参数 z = a*x + b*y + c
    返回: 2D图像，shape (H, W)
    """
    D, H, W = volume_data.shape

    # 构建网格
    yv, xv = np.meshgrid(np.arange(H), np.arange(W), indexing='ij')

    # 计算 z 并取出对应体素
    z = a * xv + b * yv + c
    z = np.clip(np.round(z), 0, D-1).astype(np.int32)
    sampled = volume_data[z, yv, xv]
    return sampled

def sample_plane_from_volume_roi(volume_data, a, b, c, x0, y0, x1, y1):
    """
    在指定ROI内按平面采样，返回局部复合图（尺寸为 (y1-y0, x1-x0)）。
    """
    D, H, W = volume_data.shape
    x0 = max(0, int(x0)); y0 = max(0, int(y0))
    x1 = min(W, int(x1)); y1 = min(H, int(y1))
    if x1 <= x0 or y1 <= y0:
        return np.zeros((0,0), dtype=volume_data.dtype)
    yv, xv = np.meshgrid(np.arange(y0, y1), np.arange(x0, x1), indexing='ij')
    z = a * xv + b * yv + c
    z = np.clip(np.round(z), 0, D-1).astype(np.int32)
    sampled = volume_data[z, yv, xv]
    return sampled

def _fit_plane(points):
    # points: list of (x,y,z)
    A = []
    b = []
    for x,y,z in points:
        A.append([x, y, 1.0])
        b.append(z)
    A = np.array(A, dtype=np.float64)
    b = np.array(b, dtype=np.float64)
    try:
        coef, _, _, _ = np.linalg.lstsq(A, b, rcond=None)
        a,b_,c = coef.tolist()
        return a, b_, c
    except Exception:
        return 0.0, 0.0, float(np.median([p[2] for p in points]))

# def _interp_z(a,b,c, x,y, Z):
#     z = a*x + b*y + c
#     return int(np.clip(round(z), 0, Z-1))

 # 1. 找到四个方向的矩形样本
def find_corner_samples(data, H=None, W=None, outlier_threshold=2.0):
    """
    选择四个最接近方形的角点，提高拟合基准框的准确性

    data: numpy 数组 (N, 7)，前 4 列为 [x1,y1,x2,y2]
    H, W: 图像高宽（如果需要参考图像坐标系右下角点）
    outlier_threshold: 离群点阈值，距离主要分布区域多少倍标准差视为离群

    返回: list，包含 4 个方向最远的样本 (完整一行数据)
    """
    if len(data) == 0:
        return []

    x1, y1, x2, y2 = data[:, 0], data[:, 1], data[:, 2], data[:, 3]

    # 计算所有框的中心点
    centers_x = (x1 + x2) / 2.0
    centers_y = (y1 + y2) / 2.0

    # 先过滤离群点：基于中心点分布的统计特性
    if len(data) > 4:  # 只有足够多的框才进行离群点过滤
        # 计算中心点的均值和标准差
        mean_x, std_x = np.mean(centers_x), np.std(centers_x)
        mean_y, std_y = np.mean(centers_y), np.std(centers_y)

        # 过滤掉距离主要分布区域过远的点
        valid_mask = (
            (np.abs(centers_x - mean_x) <= outlier_threshold * std_x) &
            (np.abs(centers_y - mean_y) <= outlier_threshold * std_y)
        )

        if np.sum(valid_mask) >= 4:  # 确保过滤后还有足够的框
            data = data[valid_mask]
            x1, y1, x2, y2 = data[:, 0], data[:, 1], data[:, 2], data[:, 3]
            centers_x = (x1 + x2) / 2.0
            centers_y = (y1 + y2) / 2.0
            print(f"离群点过滤: 保留{np.sum(valid_mask)}/{len(valid_mask)}个框")

    if W is None: W = np.max(x2)
    if H is None: H = np.max(y2)

    # 使用基于方形度的角点选择算法
    return _find_square_corners(data, centers_x, centers_y, H, W)

def _find_square_corners(data, centers_x, centers_y, H, W):
    """
    基于相邻性的简化角点选择算法
    1. 先选择距离四个角落最近的0类框
    2. 检查相邻框的坐标对齐性（上下框y坐标接近，左右框x坐标接近）
    3. 如果不符合，用第二近的框替换

    Args:
        data: 检测框数据
        centers_x, centers_y: 中心点坐标
        H, W: 图像尺寸

    Returns:
        list: 四个角点数据
    """
    corner_start_time = time.time()

    if len(data) < 4:
        return data.tolist()

    # 只考虑0类框（单圆）
    # 数据格式: [x1, y1, x2, y2, zi, conf, cls]，类别在第6列（索引6）
    if data.shape[1] > 6:
        class_0_mask = data[:, 6] == 0  # 类别在第6列
        if np.sum(class_0_mask) < 4:
            # 如果0类框不够4个，使用所有框
            class_0_indices = list(range(len(data)))
        else:
            class_0_indices = np.where(class_0_mask)[0].tolist()
    else:
        # 如果数据格式不对，使用所有框
        class_0_indices = list(range(len(data)))

    # 为每个角落找到最近的两个候选框
    corners = {
        'top_left': [],     # (距离, 索引, x, y)
        'top_right': [],
        'bottom_left': [],
        'bottom_right': []
    }

    for idx in class_0_indices:
        cx, cy = centers_x[idx], centers_y[idx]

        # 计算到四个角落的距离
        dist_tl = cx**2 + cy**2
        dist_tr = (W - cx)**2 + cy**2
        dist_bl = cx**2 + (H - cy)**2
        dist_br = (W - cx)**2 + (H - cy)**2

        corners['top_left'].append((dist_tl, idx, cx, cy))
        corners['top_right'].append((dist_tr, idx, cx, cy))
        corners['bottom_left'].append((dist_bl, idx, cx, cy))
        corners['bottom_right'].append((dist_br, idx, cx, cy))

    # 为每个角落排序，取前2个候选
    for corner in corners.values():
        corner.sort(key=lambda x: x[0])  # 按距离排序

    # 初始选择：每个角落选择最近的框
    selected = {
        'top_left': corners['top_left'][0] if corners['top_left'] else None,
        'top_right': corners['top_right'][0] if corners['top_right'] else None,
        'bottom_left': corners['bottom_left'][0] if corners['bottom_left'] else None,
        'bottom_right': corners['bottom_right'][0] if corners['bottom_right'] else None
    }

    # 检查相邻性并优化选择
    tolerance = 20  # 坐标对齐容差

    # 检查上边：左上和右上的y坐标应该接近
    if selected['top_left'] and selected['top_right']:
        y_diff = abs(selected['top_left'][3] - selected['top_right'][3])
        if y_diff > tolerance:
            # 尝试替换右上角
            if len(corners['top_right']) > 1:
                alt_tr = corners['top_right'][1]
                if abs(selected['top_left'][3] - alt_tr[3]) < y_diff:
                    selected['top_right'] = alt_tr

    # 检查下边：左下和右下的y坐标应该接近
    if selected['bottom_left'] and selected['bottom_right']:
        y_diff = abs(selected['bottom_left'][3] - selected['bottom_right'][3])
        if y_diff > tolerance:
            # 尝试替换右下角
            if len(corners['bottom_right']) > 1:
                alt_br = corners['bottom_right'][1]
                if abs(selected['bottom_left'][3] - alt_br[3]) < y_diff:
                    selected['bottom_right'] = alt_br

    # 检查左边：左上和左下的x坐标应该接近
    if selected['top_left'] and selected['bottom_left']:
        x_diff = abs(selected['top_left'][2] - selected['bottom_left'][2])
        if x_diff > tolerance:
            # 尝试替换左下角
            if len(corners['bottom_left']) > 1:
                alt_bl = corners['bottom_left'][1]
                if abs(selected['top_left'][2] - alt_bl[2]) < x_diff:
                    selected['bottom_left'] = alt_bl

    # 检查右边：右上和右下的x坐标应该接近
    if selected['top_right'] and selected['bottom_right']:
        x_diff = abs(selected['top_right'][2] - selected['bottom_right'][2])
        if x_diff > tolerance:
            # 尝试替换右下角
            if len(corners['bottom_right']) > 1:
                alt_br = corners['bottom_right'][1]
                if abs(selected['top_right'][2] - alt_br[2]) < x_diff:
                    selected['bottom_right'] = alt_br

    # 提取最终选择的索引
    result_indices = []
    corner_info = []
    for corner_name, corner_data in selected.items():
        if corner_data:
            idx = corner_data[1]  # 索引在第二个位置
            cx, cy = corner_data[2], corner_data[3]  # 中心坐标
            result_indices.append(idx)
            corner_info.append(f"{corner_name}({cx:.1f},{cy:.1f})")

    # 检查是否有重复的框被选为多个角点
    unique_indices = list(set(result_indices))
    if len(unique_indices) < len(result_indices):
        print(f"    警告: 检测到重复角点，原始{len(result_indices)}个，去重后{len(unique_indices)}个")
        print(f"    角点详情: {corner_info}")

    # 确保有4个不同的框
    if len(unique_indices) < 4:
        # 补充缺失的框：选择距离四个角落最近但未被选中的框
        all_indices = set(class_0_indices)
        remaining = list(all_indices - set(unique_indices))

        if remaining:
            # 按距离角落的远近排序补充
            corner_distances = []
            for idx in remaining:
                cx, cy = centers_x[idx], centers_y[idx]
                min_corner_dist = min(
                    cx**2 + cy**2,                    # 到左上角距离
                    (W-cx)**2 + cy**2,               # 到右上角距离
                    cx**2 + (H-cy)**2,               # 到左下角距离
                    (W-cx)**2 + (H-cy)**2            # 到右下角距离
                )
                corner_distances.append((idx, min_corner_dist))

            # 按距离排序，选择距离角落最近的点补充
            corner_distances.sort(key=lambda x: x[1])
            for idx, _ in corner_distances[:4-len(unique_indices)]:
                unique_indices.append(idx)
                cx, cy = centers_x[idx], centers_y[idx]
                corner_info.append(f"补充({cx:.1f},{cy:.1f})")

    corner_time = time.time() - corner_start_time
    print(f"    角点选择算法: {corner_time:.4f}s")
    print(f"    选中角点: {corner_info}")

    return [data[i] for i in unique_indices[:4]]

def find_outlier_and_predict_fourth(samples):
    """
    输入:
        samples: np.ndarray, 形状 (4, 7), 每行 [x1, y1, x2, y2, ...]
    输出:
        outlier_idx: int, 最不合适的样本索引
        predicted_rect: [x1, y1, x2, y2], 根据其他三个矩形推测的理想矩形
    """
    assert samples.shape[0] == 4 and samples.shape[1] >= 4, "必须有 4 个样本，每个至少 4 个列"

    # 提取每个样本的左上右下坐标
    rects = samples[:, :4].astype(float)

    # 计算每个矩形的中心点
    centers = np.stack([(rects[:,0]+rects[:,2])/2, (rects[:,1]+rects[:,3])/2], axis=1)

    # 计算每个矩形中心与其他三个中心的平均距离
    avg_distances = []
    for i in range(4):
        others = np.delete(centers, i, axis=0)
        dists = np.linalg.norm(others - centers[i], axis=1)
        avg_distances.append(dists.mean())

    avg_distances = np.array(avg_distances)
    outlier_idx = np.argmin(avg_distances)  # 最不合适的矩形

    # 根据其他三个矩形推测第四个理想矩形坐标
    included = [i for i in range(4) if i != outlier_idx]
    x1_pred = rects[included,0].max()
    y1_pred = rects[included,1].max()
    x2_pred = rects[included,2].max()
    y2_pred = rects[included,3].max()
    predicted_rect = [x1_pred, y1_pred, x2_pred, y2_pred]

    return outlier_idx, predicted_rect

def load_volume_from_png_folder(folder_path):
    file_name_list = os.listdir(folder_path)
    file_name_list.sort( key = lambda x: float(x.split('_')[-1][:-4]))
    # print(file_name_list)
    # for i in file_name_list:
    #     print("load:", i)
    volume_data = []
    for file_name in file_name_list:
        if file_name[-4:] == ".png" and 'long' not in file_name:
            image = cv2.imread(folder_path + file_name , -1)
            assert image is not None, f"{folder_path + file_name}"
            if (image>0).sum() > image.shape[0]*image.shape[1]*0.5:
                volume_data.append(image)
        else:
            continue
    return np.array(volume_data)

# 兼容旧代码的函数
def auto_focus(volume_data: np.ndarray, grid_size = 4) ->Tuple[List, List] :
    """兼容旧代码的自动对焦函数
    参数:
        volume_data: 输入数据，形状为 (height, width, num_layers)
        grid_size: 网格大小（如 3 表示 3×3 网格）

    返回:
        tuple(List, int)，包含最聚焦的切片和最聚焦的层索引

    """
    config = {
        'auto_focus': {
            'grid_size': grid_size,
            'peak_range_ratio': 0.1
        }
    }

    processor = AutoFocusProcessor(config)
    return processor.generate_best_slice(volume_data)


# Legacy/Inactive: module self-test entry not used in production
# if __name__ == "__main__":
#     import matplotlib.pyplot as plt
#     folder_path = "/data/lijunlin/data/CT/PCB/rec_png/..."
#     volume_data = load_volume_from_png_folder(folder_path)
#     slice, slice_idx = auto_focus(volume_data)
#     plt.imshow(slice, cmap='gray')
#     plt.title(slice_idx)
#     plt.show()
