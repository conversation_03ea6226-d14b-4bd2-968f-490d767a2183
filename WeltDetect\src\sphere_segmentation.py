"""
3D球体分割模块：基于区域生长
"""
import numpy as np
import cv2
from scipy import ndimage
from scipy.ndimage import binary_fill_holes
from skimage.measure import label, regionprops
from skimage.morphology import binary_closing, binary_opening, ball
from skimage.segmentation import flood_fill
from skimage.filters import threshold_otsu
from concurrent.futures import ThreadPoolExecutor
import time
import os

class SphereSegmentation:
    def __init__(self, gradient_threshold=100, min_sphere_size=30,
                 intensity_tolerance_low=0.85,
                 max_radius=10, debug_mode=True,
                 use_spherical_constraint=True):
        """
        初始化球体分割器

        Args:
            gradient_threshold: 区域生长的梯度阈值（目前未使用，保留接口）
            min_sphere_size: 最小球体体素数（3D总像素数，过滤小噪声）
            intensity_tolerance_low: 强度下限容忍度（种子CT值的倍数，如0.85表示种子值的85%）
            intensity_tolerance_high: 强度上限容忍度（种子CT值的倍数，如1.15表示种子值的115%）
            max_radius: 最大生长半径（从种子点向各方向的最大距离，单位：像素）
            debug_mode: 调试模式开关（控制详细输出和中间结果保存）

        区域生长条件说明:
        1. 强度条件: 像素CT值必须在 [种子值*low, 种子值*high] 范围内
        2. 距离条件: 像素必须在种子点的max_radius范围内（3D球形区域）
        3. 连通性条件: 像素必须与种子点6连通（上下左右前后相邻）
        4. 大小过滤: 最终生长区域必须≥min_sphere_size个像素

        注意: 目前未使用梯度条件，主要依靠强度阈值和连通性
        """
        self.gradient_threshold = gradient_threshold  # 保留但未使用
        self.intensity_tolerance_low = intensity_tolerance_low
        self.max_radius = max_radius
        self.debug_mode = debug_mode
        self.use_spherical_constraint = use_spherical_constraint
        # 默认最小体素数去除（关闭最小球体体积过滤），保留属性以兼容旧逻辑
        self.min_sphere_size = 0

    def _auto_threshold(self, arr):
        vals = arr.astype(np.float32)
        if vals.size < 32:
            return float(vals.mean())
        vmin, vmax = float(vals.min()), float(vals.max())
        if vmin >= vmax:
            return float(vmin)
        try:
            th = threshold_otsu(vals)
        except Exception:
            mu, sigma = float(vals.mean()), float(vals.std())
            th = mu + 0.5 * sigma
        return float(th)

    def segment_spheres_binary(self, volume_data, detections, focus_layer):
        """
        基于类别与几何约束的快速二值化分割：
        - 类别1：以检测框中心为球心，半径=0.5*max(w,h)，Z中心为对焦层；在球形约束内进行二值化
        - 非类别1：XY约束为检测框；Z长度=类别1框的平均边长（各取一半向两端扩展）；在该柱状区域内二值化
        返回：mask列表、种子点列表
        """
        Z, H, W = volume_data.shape
        if len(detections) == 0:
            return [], []

        # 统计类别
        cls_list = []
        for det in detections:
            if len(det) >= 6:
                cls_list.append(int(det[5]))
            else:
                cls_list.append(1)
        unique, counts = np.unique(np.array(cls_list), return_counts=True)
        print(f"检测类别统计: {len(unique)} 类 -> { {int(k): int(v) for k, v in zip(unique, counts)} }")

        # 计算类别1平均边长
        sides = [max(int(det[2]-det[0]), int(det[3]-det[1])) for det, c in zip(detections, cls_list) if c == 1]
        avg_side = int(np.mean(sides)) if len(sides) > 0 else None
        if avg_side is None:
            all_sides = [max(int(det[2]-det[0]), int(det[3]-det[1])) for det in detections]
            avg_side = int(np.median(all_sides)) if len(all_sides) > 0 else 3

        masks, seeds = [], []
        for det, c in zip(detections, cls_list):
            x1, y1, x2, y2 = map(int, det[:4])
            x1 = max(0, min(x1, W - 1)); y1 = max(0, min(y1, H - 1))
            x2 = max(x1 + 1, min(x2, W)); y2 = max(y1 + 1, min(y2, H))
            cx = (x1 + x2) // 2
            cy = (y1 + y2) // 2
            seeds.append((focus_layer, cy, cx))

            if c == 1:
                diam = int(max(x2 - x1, y2 - y1))
                diam = max(1, diam)
                r_float = diam / 2.0
                # 使裁剪长度在XYZ均为直径(整数)，中心在focus_layer/cx/cy
                z0 = max(0, int(focus_layer - np.floor(r_float)))
                z1 = min(Z, z0 + diam)
                y0 = max(0, int(cy - np.floor(r_float)))
                y1r = min(H, y0 + diam)
                x0 = max(0, int(cx - np.floor(r_float)))
                x1r = min(W, x0 + diam)
                sub = volume_data[z0:z1, y0:y1r, x0:x1r]
                th = self._auto_threshold(sub)
                bin_sub = sub >= th
                # 球形约束（XYZ三轴半径相等，直径=检测框最大边）
                dz, dy, dx = np.ogrid[z0:z1, y0:y1r, x0:x1r]
                sphere = (dz - focus_layer) ** 2 + (dy - cy) ** 2 + (dx - cx) ** 2 <= (r_float * r_float)
                bin_sub &= sphere
                m = np.zeros_like(volume_data, dtype=bool)
                m[z0:z1, y0:y1r, x0:x1r] = bin_sub
                masks.append(m)
            else:
                # 非1类：矩形XY，Z长度=avg_side
                z_half = max(1, avg_side // 2)
                z0 = max(0, focus_layer - z_half)
                z1 = min(Z, focus_layer + z_half + 1)
                sub = volume_data[z0:z1, y1:y2, x1:x2]
                th = self._auto_threshold(sub)
                bin_sub = sub >= th
                m = np.zeros_like(volume_data, dtype=bool)
                m[z0:z1, y1:y2, x1:x2] = bin_sub
                masks.append(m)
    
    def _refine_center_and_diameter(self, volume_data, det):
        """
        在球心层的检测框内做二维二值分割，细化球心(cx,cy)与直径：
        - 使用检测框自带的 cz=det[4] 作为中心层
        - 在该层的检测框ROI内自适应阈值分割
        - 计算分割结果的最小外接矩形；球心=外接矩形中心；直径=外接矩形较长边
        - 若分割为空，则回退到原始检测框中心与直径
        """
        Z, H, W = volume_data.shape
        x1,y1,x2,y2 = map(int, det[:4])
        x1 = max(0, min(x1, W-1)); y1 = max(0, min(y1, H-1))
        x2 = max(x1+1, min(x2, W)); y2 = max(y1+1, min(y2, H))
        cx0 = (x1 + x2) // 2
        cy0 = (y1 + y2) // 2
        diam0 = max(int(x2 - x1), int(y2 - y1))
        cz = int(det[4]) if len(det) >= 5 else 0
        cz = max(0, min(cz, Z-1))

        roi = volume_data[cz, y1:y2, x1:x2]
        if roi.size == 0:
            return cx0, cy0, max(1, diam0)
        th = self._auto_threshold(roi)
        mask = roi >= th
        if not np.any(mask):
            return cx0, cy0, max(1, diam0)
        ys = np.where(mask.any(axis=1))[0]
        xs = np.where(mask.any(axis=0))[0]
        y_top = int(ys[0]); y_bot = int(ys[-1]) + 1
        x_left = int(xs[0]); x_right = int(xs[-1]) + 1
        rx1 = x1 + x_left; rx2 = x1 + x_right
        ry1 = y1 + y_top;  ry2 = y1 + y_bot
        rx1 = max(0, min(rx1, W-1)); ry1 = max(0, min(ry1, H-1))
        rx2 = max(rx1+1, min(rx2, W)); ry2 = max(ry1+1, min(ry2, H))
        rcx = (rx1 + rx2) // 2
        rcy = (ry1 + ry2) // 2
        rdiam = max(int(rx2 - rx1), int(ry2 - ry1))
        return rcx, rcy, max(1, rdiam)
    
    def segment_spheres_binary(self, volume_data, detections, composite=None):
        """
        固定类别的分割策略：
        - 类0/2：先在复合图（球心层）上调用_refine_center_and_diameter，得到更精确的(cx,cy,diam)，据此建立球形约束，再在体数据中进行二值化分割。
        - 类1：保持原策略，矩形XY约束、Z厚度=类0平均边长。
        返回：mask列表、种子点列表
        """
        Z, H, W = volume_data.shape
        if len(detections) == 0:
            return [], []

        # 统计类别（以 det[-1] 为类别索引），并计算各类边长平均值
        cls_list = []
        by_cls_sides = {}
        for det in detections:
            cid = int(det[-1]) if len(det) >= 6 else 0
            cls_list.append(cid)
            x1,y1,x2,y2 = map(int, det[:4])
            side = max(int(x2-x1), int(y2-y1))
            by_cls_sides.setdefault(cid, []).append(side)

        # 固定语义：0=单圆，1=粘连，2=小异常，3=大外接
        # 类0平均边长供“类1的Z厚度”使用
        if 0 in by_cls_sides and len(by_cls_sides[0])>0:
            avg_side = int(round(np.mean(by_cls_sides[0])))
        else:
            all_sides = [s for v in by_cls_sides.values() for s in v]
            avg_side = int(round(np.mean(all_sides))) if len(all_sides)>0 else 3

        masks, seeds = [], []
        for det, c in zip(detections, cls_list):
            # 跳过类3（大外接框）
            if c == 3:
                continue

            x1,y1,x2,y2 = map(int, det[:4])
            x1 = max(0, min(x1, W-1)); y1 = max(0, min(y1, H-1))
            x2 = max(x1+1, min(x2, W)); y2 = max(y1+1, min(y2, H))
            cx = (x1 + x2) // 2
            cy = (y1 + y2) // 2
            cz = int(det[4])  # if len(det) >= 5 else int(focus_layer)
            seeds.append((cz, cy, cx))

            if c in (0, 2):
                # 优先在复合图上细化球心与直径（复合图更接近真实球心层）
                if composite is not None:
                    rcx, rcy, rdiam = self._refine_center_and_diameter(np.expand_dims(composite, axis=0), det)
                else:
                    rcx, rcy, rdiam = self._refine_center_and_diameter(volume_data, det)
                r = max(1, int(round(rdiam))) / 2.0
                z0 = max(0, int(cz - np.floor(r)))
                z1 = min(Z, z0 + int(round(rdiam)))
                y0 = max(0, int(rcy - np.floor(r)))
                y1r = min(H, y0 + int(round(rdiam)))
                x0 = max(0, int(rcx - np.floor(r)))
                x1r = min(W, x0 + int(round(rdiam)))
                sub = volume_data[z0:z1, y0:y1r, x0:x1r]
                dz, dy, dx = np.ogrid[z0:z1, y0:y1r, x0:x1r]
                sphere = (dz - cz)**2 + (dy - rcy)**2 + (dx - rcx)**2 <= (r*r)
                sphere_vals = sub[sphere]
                th = self._auto_threshold(sphere_vals)
                bin_sub = np.zeros_like(sub, dtype=bool)
                bin_sub[sphere] = sphere_vals >= th
                m = np.zeros_like(volume_data, dtype=bool)
                m[z0:z1, y0:y1r, x0:x1r] = bin_sub
                masks.append(m)
            else:
                # 类1（粘连）：XY=框，Z=avg_side（以cz为中心）
                diam_z = max(1, int(avg_side))
                z0 = max(0, int(cz - np.floor(diam_z / 2.0)))
                z1 = min(Z, z0 + diam_z)
                sub = volume_data[z0:z1, y1:y2, x1:x2]
                th = self._auto_threshold(sub)
                bin_sub = sub >= th
                m = np.zeros_like(volume_data, dtype=bool)
                m[z0:z1, y1:y2, x1:x2] = bin_sub
                masks.append(m)

        return masks, seeds

    def save_3d_segmentation_result_with_rand_bg(self, volume_data, sphere_masks, output_dir="./data/output"):
        """
        保存3D分割结果为ImageJ可读取的二进制文件

        Args:
            volume_data: 原始3D体数据
            sphere_masks: 球体分割mask列表
            output_dir: 输出目录
        """

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 获取数据维度
        Z, H, W = volume_data.shape

        # 创建合并的3D分割结果
        # 保留原始体素值，背景设为0
        print(f"正在生成3D分割结果...")
        print(f"原始数据维度: {Z} x {H} x {W}")
        print(f"原始数据类型: {volume_data.dtype}")
        print(f"球体数量: {len(sphere_masks)}")

        # 使用与原始数据相同的数据类型
        segmented_volume = np.zeros((Z, H, W), dtype=volume_data.dtype)
        correct_volume = np.zeros((Z, H, W), dtype=volume_data.dtype)

        max_value_scale = 0.5

        # 合并所有球体mask
        t0 = time.time()
        combined_mask = np.zeros((Z, H, W), dtype=bool)
        for mask in sphere_masks:
            combined_mask |= mask
        t_merge = time.time() - t0

        # 计算2D切片层面的空洞，一次性完成
        t1 = time.time()
        hole_mask = np.zeros((Z, H, W), dtype=bool)
        for zz in range(Z):
            s = combined_mask[zz]
            if np.any(s):
                filled = ndimage.binary_fill_holes(s)
                hole_mask[zz] = filled & (~s)
        t_holes = time.time() - t1

        # 连通区域标记（3D）
        t2 = time.time()
        structure = ndimage.generate_binary_structure(3, 3)
        labeled_mask, num_features = ndimage.label(combined_mask, structure=structure)
        t_label = time.time() - t2
        print(f"连通区域数量: {num_features}")

        # 每个连通区域的最大值（向量化）
        t3 = time.time()
        if num_features > 0:
            indices = np.arange(1, num_features + 1)
            comp_max = ndimage.maximum(volume_data, labels=labeled_mask, index=indices)
        else:
            comp_max = np.array([], dtype=volume_data.dtype)
        t_compmax = time.time() - t3

        # 一次性膨胀
        t4 = time.time()
        dilated_combined_mask = ndimage.binary_dilation(combined_mask, structure=structure, iterations=1)
        # 传播标签到膨胀边界（近邻最大标签近似）
        dilated_labels = ndimage.grey_dilation(labeled_mask.astype(np.int32), footprint=structure)
        t_dilate = time.time() - t4

        # 生成校正体素（仅在膨胀区域内）
        t5 = time.time()
        idx = dilated_combined_mask
        if np.any(idx) and num_features > 0:
            lbl_flat = dilated_labels[idx]
            valid = lbl_flat > 0
            vals = volume_data[idx]
            corr = np.zeros_like(vals)
            if np.any(valid):
                max_vals = comp_max[lbl_flat[valid] - 1]
                corr_valid = (1 - max_value_scale) * vals[valid] + max_value_scale * max_vals
                corr[valid] = corr_valid
            correct_volume[idx] = corr
        t_corr = time.time() - t5

        # 合成结果并填充背景
        t6 = time.time()
        segmented_volume[dilated_combined_mask] = correct_volume[dilated_combined_mask]
        bg_mask = ~dilated_combined_mask
        bg_values = volume_data[bg_mask]
        if bg_values.size > 0:
            random_bg_values = np.random.choice(bg_values, size=bg_values.size, replace=True)
            segmented_volume[bg_mask] = (random_bg_values * 0.7).astype(volume_data.dtype)
        # 保持空洞为0
        segmented_volume[hole_mask] = 0
        t_bg = time.time() - t6

        if self.debug_mode:
            print(f"连通/修正耗时: 合并mask {t_merge:.3f}s, 空洞 {t_holes:.3f}s, 标记 {t_label:.3f}s, 每连通最大 {t_compmax:.3f}s, 膨胀/传播 {t_dilate:.3f}s, 校正 {t_corr:.3f}s, 背景 {t_bg:.3f}s")


        # 保存全体 Z 切片
        data_type_str = str(volume_data.dtype)
        filename_all = f"processed_allSlice_{W}x{H}x{Z}.raw"
        output_path_all = os.path.join(output_dir, filename_all)
        segmented_volume.tofile(output_path_all)
        print(f"分割结果已保存(全Z层): {output_path_all}")
        # 写同名ini
        ini_path = os.path.splitext(output_path_all)[0] + ".ini"
        try:
            with open(ini_path, "w", encoding="utf-8", newline="\n") as f:
                f.write("[RawImageInfo]\n")
                f.write(f"BitsAllocated={16 if '16' in data_type_str else 8}\n")
                f.write(f"Frames={Z}\n")
                f.write(f"Height={H}\n")
                f.write(f"Width={W}\n")
        except Exception:
            pass
        print(f"ImageJ打开参数: 宽度={W}, 高度={H}, 切片数={Z}, 数据类型={data_type_str}")


        # 导出聚焦层PNG：来自最终分割体的聚焦层切片

        # z_idx = int(min(max(focus_layer, 0), Z - 1))
        # focus_slice = segmented_volume[z_idx]
        # if focus_slice.dtype != np.uint8:
        #     fs = focus_slice.astype(np.float32)
        #     fs = (fs - fs.min()) / (fs.max() - fs.min() + 1e-6)
        #     focus_img = (fs * 255).astype('uint8')
        # else:
        #     focus_img = focus_slice
        # focus_png_path = os.path.join(output_dir, "focus_layer_segmentation.png")
        # cv2.imwrite(focus_png_path, focus_img)

        return output_path_all

    # def fit_mask_to_sphere(self, mask):
    #     """
    #     原后处理函数，现已由生长阶段球形约束替代；保留作参考
    #     """
    #     if not np.any(mask):
    #         return mask
    #     coords = np.where(mask)
    #     if len(coords[0]) == 0:
    #         return mask
    #     centroid_z = np.mean(coords[0]); centroid_y = np.mean(coords[1]); centroid_x = np.mean(coords[2])
    #     layer_mask = mask[int(np.round(centroid_z))]
    #     if not np.any(layer_mask):
    #         return mask
    #     layer_coords = np.where(layer_mask)
    #     if len(layer_coords[0]) == 0:
    #         return mask
    #     distances = np.sqrt((layer_coords[0] - centroid_y)**2 + (layer_coords[1] - centroid_x)**2)
    #     radius = np.percentile(distances, 99)
    #     fitted_mask = np.zeros_like(mask, dtype=bool)
    #     for z, y, x in zip(coords[0], coords[1], coords[2]):
    #         distance_3d = np.sqrt((z - centroid_z)**2 + (y - centroid_y)**2 + (x - centroid_x)**2)
    #         if distance_3d <= radius:
    #             fitted_mask[z, y, x] = True
    #     return fitted_mask

 # Legacy/Inactive: seed point generation not used in current workflow
    # def find_seed_points(self, volume_data, detections, focus_layer):
    #     """Generate seed points (unused)."""
    #     return []
    # Legacy/Inactive: median diameter computation not used in current workflow
    # def compute_single_ball_median_diameter(self, detections):
    #     """Compute median diameter (unused)."""
    #     return None


    # Legacy/Inactive: region growing is not used in current workflow
    # def region_growing_3d_improved(self, volume_data, seed_point, detection_box, max_radius=15):
    #     """3D region growing (unused)."""
    #     return np.zeros((volume_data.shape[0], volume_data.shape[1], volume_data.shape[2]), dtype=bool)

    # Legacy/Inactive: simple region growing is not used in current workflow
    # def simple_region_growing(self, volume_data, seed_point, detection_box):
    #     """Simple region growing (unused)."""
    #     return np.zeros((volume_data.shape[0], volume_data.shape[1], volume_data.shape[2]), dtype=bool)


    # Legacy/Inactive: sphere fitting not used in current workflow
    # def fit_mask_to_detection_sphere(self, mask, center_x, center_y, center_z, radius):
    #     """Sphere fitting (unused)."""
    #     return mask
    
    # 按类别分割球体，基于检测框和二值化，估算球心和半径，裁剪为球形区域
    # Legacy/Inactive: diff class segmentation not used in current workflow
    # def segment_spheres_for_diff_class(self, volume_data, detections):
    #     """Diff class segmentation (unused)."""
    #     return [], []

    # Legacy/Inactive: segment_spheres not used in current workflow
    # def segment_spheres(self, volume_data, detections, focus_layer):
    #     """Segment spheres (unused)."""
    #     return [], []

    # 该函数未在主流程中直接使用，保留以便调试可视化
    # def visualize_results(self, volume_data, sphere_masks, focus_layer):
    #     """
    #     可视化分割结果
    #
    #     Args:
    #         volume_data: 3D体数据
    #         sphere_masks: 球体分割结果
    #         focus_layer: 对焦层索引
    #
    #     Returns:
    #         overlay_image: 叠加显示的图像
    #     """
    #     focus_slice = volume_data[focus_layer].copy()
    #     focus_slice = ((focus_slice - focus_slice.min()) /
    #                   (focus_slice.max() - focus_slice.min()) * 255).astype(np.uint8)
    #     overlay = cv2.cvtColor(focus_slice, cv2.COLOR_GRAY2RGB)
    #     colors = [(255,0,0), (0,255,0), (0,0,255), (255,255,0),
    #              (255,0,255), (0,255,255), (128,0,128), (255,165,0)]
    #     for i, mask in enumerate(sphere_masks):
    #         color = colors[i % len(colors)]
    #         layer_mask = mask[focus_layer]
    #         overlay[layer_mask] = overlay[layer_mask] * 0.7 + np.array(color) * 0.3
    #     return overlay

    # Legacy/Inactive: save_3d_segmentation_result not used in current workflow
    # def save_3d_segmentation_result(self, volume_data, sphere_masks, focus_layer, output_dir="./data/output"):
    #     """Save 3D segmentation result (unused)."""
    #     return "", ""
