# YOLO ONNX C++ 推理 (OpenCV 4.8.0 + ONNXRuntime GPU)

本项目实现了基于 **OpenCV 4.8.0** 和 **ONNX Runtime GPU** 的 YOLO 推理，支持：
- 动态输入模型（输入尺寸可变）
- 固定输入模型（自动 resize）
- 批量推理
- NMS（非极大值抑制）
- 绘制检测结果到图像
- 同时支持返回检测结果数据结构，便于二次处理

---

## 🚀 环境配置

### 1. 系统依赖
- C++17 或更高版本
- CUDA( 没测试 )/CPU ( CPU avg time cost 110ms)
- OpenCV 4.8.0
- ONNX Runtime GPU
---

### 2. 安装 OpenCV 4.8.0
下载 opencv release库 并安装，安装路径本质上是一个解压路径

```bash
https://github.com/opencv/opencv/releases/download/4.8.0/opencv-4.8.0-windows.exe
```

### 3. 安装 ONNX Runtime GPU
去 ONNX Runtime 官方发布页 如下链接下载对应的 onnxruntime-gpu 压缩包，并解压 ：

```bash
https://github.com/microsoft/onnxruntime/releases/download/v1.22.1/onnxruntime-win-x64-gpu-1.22.1.zip
```

### 4. Microsoft Visual Studio 属性页配置
```bash
包含目录

$(ThirdPartyLibraries)\opencv-4.8.0\opencv\build\include;
$(CUDA_PATH)\include;$(ThirdPartyLibraries)\ipp_2020.0.166\include;
$(ThirdPartyLibraries)\onnxruntime-win-x64-gpu-1.22.1\include;
$(IncludePath)

库目录
$(ThirdPartyLibraries)\opencv-4.8.0\opencv\build\x64\vc16\bin;
$(ThirdPartyLibraries)\onnxruntime-win-x64-gpu-1.22.1\lib;
$(ThirdPartyLibraries)\opencv-4.8.0\opencv\build\x64\vc16\lib;
$(LibraryPath)

连接器->输入
opencv_world480d.lib;
onnxruntime.lib;
onnxruntime_providers_shared.lib;
onnxruntime_providers_cuda.lib;
%(AdditionalDependencies)

```


## 📦 项目结构
```bash
.
├── src
|   ├── Detector.h
|   ├── Detector.cpp
|   ├── ai_infer_main.cpp
|   └── sample_main.cpp
└── README.md
```

## 🛠 使用方法

### 1. 准备模型

将 YOLO 的 .onnx 模型放到 Config/ 目录中。
支持两种模型：

- 固定输入（例如 1x3x640x640）
- 动态输入（例如 batch,3,height,width，height 和 width 为 -1）

### 2. 调用示例

```cpp
#include "Detector.h"
#include <iostream>
#include <filesystem>
 
int main() {
	try {
		
		YoloOnnxDetector detector("D:/work/Code/OnnxYolo-main/OnnxYolo-main/x64/Debug/Config/bestV0.onnx", false, 2);

		// 预留
		detector.LoadLabels({"0"}); 

		cv::Mat img = cv::imread("D:/work/Code/OnnxYolo-main/OnnxYolo-main/x64/Debug/1.jpg");
		if (img.empty()) {
			std::cerr << "读取图片失败\n";
			return -1;
		}

		cv::Mat result;
		std::vector<Detection> detections = detector.Detect(img);

		/*
		
		struct Detection {
			cv::Rect box;      // box.x = center x  box.y = center y  box.width = w  box.height = h 
			int classId;
			float confidence;
		};
		
		*/

		result = img.clone();
		detector.DrawDetections( result, detections); // 画图

		cv::imshow("Result", result);
		cv::imwrite("./output.jpg", result);
		cv::waitKey(0);
	}
	catch (const std::exception& ex) {
		std::cerr << "推理错误: " << ex.what() << std::endl;
		return -1;
	}
	return 0;
}

```

## 🔍 API 说明


### YoloOnnxDetector 类

| 方法                             | 说明                           |
|---------------------------------|--------------------------------|
| `YoloOnnxDetector(modelPath, useCuda, numThreads)` | 构造函数，加载指定路径的模型，是否启用 CUDA，线程数 |
| `void LoadLabels(const std::vector<std::string>& labels)` | 加载类别标签，用于绘制和识别 |
| `std::vector<Detection> Detect(const cv::Mat& image)` | 输入图片，执行检测，返回检测结果列表 |
| `void DrawDetections(cv::Mat& image, const std::vector<Detection>& detections)` | 在图片上绘制检测框和标签 |

---

### Detection 结构体

```cpp
struct Detection {
    int classId;          // 类别 ID
    float score;          // 置信度
    std::array<float, 4> box; // 检测框 [center_x, center_y, width, height]
};

YoloOnnxDetector detector("model.onnx", true, 4);
detector.LoadLabels({"person", "car", "bicycle"});

cv::Mat image = cv::imread("test.jpg");
auto detections = detector.Detect(image);

detector.DrawDetections(image, detections);
cv::imwrite("result.jpg", image);

```

