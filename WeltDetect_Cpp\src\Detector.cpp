#include "Detector.h"
#include <fstream>
#include <chrono>
#include <algorithm>
#include <iostream>
#include <vector>
#include <map>
#include <numeric> 

int find_best_scale(int srcW)
{
    int table_idx[] = { 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096 };
    int table_size = 12;

    int best_match = 0;
    if (srcW < table_idx[0])
    {
        best_match = 6;
    }
    else if (srcW > table_idx[11])
    {
        best_match = 9;
    }
    else
    {
        for (int i = 0; i < table_size - 1; i++)
        {
            if (srcW == table_idx[i])
            {
                return table_idx[i];
            }
            else if (table_idx[i]< srcW && table_idx[i + 1]> srcW)
            {
                return  table_idx[i + 1]; // (table_idx[i] + table_idx[i + 1]) / 2;
            }
        }
    }
    return table_idx[best_match];
}

// 计算 IoU
float IoU(const cv::Rect& a, const cv::Rect& b) {
    int inter_x1 = std::max(a.x, b.x);
    int inter_y1 = std::max(a.y, b.y);
    int inter_x2 = std::min(a.x + a.width, b.x + b.width);
    int inter_y2 = std::min(a.y + a.height, b.y + b.height);

    int inter_w = std::max(0, inter_x2 - inter_x1);
    int inter_h = std::max(0, inter_y2 - inter_y1);
    int inter_area = inter_w * inter_h;

    int union_area = a.area() + b.area() - inter_area;
    return union_area > 0 ? float(inter_area) / union_area : 0.f;
}

// NMS
std::vector<int> NMS(const std::vector<cv::Rect>& boxes, const std::vector<float>& scores, float iouThreshold) {
    std::vector<int> indices(boxes.size());
    
    std::iota(indices.begin(), indices.end(), 0);
    // 按分数降序排序
    std::sort(indices.begin(), indices.end(), [&](int i, int j) { return scores[i] > scores[j]; });

    std::vector<int> keep;
    std::vector<bool> removed(boxes.size(), false);

    for (size_t _i = 0; _i < indices.size(); ++_i) {
        int i = indices[_i];
        if (removed[i]) continue;
        keep.push_back(i);
        for (size_t _j = _i + 1; _j < indices.size(); ++_j) {
            int j = indices[_j];
            if (removed[j]) continue;
            if (IoU(boxes[i], boxes[j]) > iouThreshold)
                removed[j] = true;
        }
    }
    return keep;
}

// 带类别优先级的 NMS
std::vector<int> NMSWithPriority(
    const std::vector<cv::Rect>& boxes,
    const std::vector<float>& scores,
    const std::vector<int>& classIds,
    const std::vector<int>& classPriority,
    float iouThreshold
) {
    std::vector<int> indices(boxes.size());
    std::iota(indices.begin(), indices.end(), 0);

    // 排序：先按类别优先级，再按置信度降序
    std::sort(indices.begin(), indices.end(), [&](int i, int j) {
        auto rank_i = std::find(classPriority.begin(), classPriority.end(), classIds[i]) - classPriority.begin();
        auto rank_j = std::find(classPriority.begin(), classPriority.end(), classIds[j]) - classPriority.begin();
        if (rank_i != rank_j) return rank_i < rank_j; // 优先级小的先
        return scores[i] > scores[j];                // 同优先级按置信度
        });

    std::vector<int> keep;
    std::vector<bool> removed(boxes.size(), false);

    for (size_t _i = 0; _i < indices.size(); ++_i) {
        int i = indices[_i];
        if (removed[i]) continue;
        keep.push_back(i);
        for (size_t _j = _i + 1; _j < indices.size(); ++_j) {
            int j = indices[_j];
            if (removed[j]) continue;
            if (IoU(boxes[i], boxes[j]) > iouThreshold)
                removed[j] = true;
        }
    }
    return keep;
}

// xywh -> xyxy 并缩放到原图尺度
cv::Rect XYWH2XYXY(float cx, float cy, float w, float h, float xFactor, float yFactor) {
    int x1 = static_cast<int>((cx - 0.5f * w) * xFactor + 0.5f);
    int y1 = static_cast<int>((cy - 0.5f * h) * yFactor + 0.5f);
    int x2 = static_cast<int>((cx + 0.5f * w) * xFactor + 0.5f);
    int y2 = static_cast<int>((cy + 0.5f * h) * yFactor + 0.5f);
    return cv::Rect(x1, y1, x2 - x1, y2 - y1);
}

// 主函数：解析输出 + NMS
std::vector<Detection> PostprocessDetections(
    const cv::Mat& detOutput,   // [num_boxes, 5 + num_classes], float32
    float xFactor, 
    float yFactor,
    float confThreshold = 0.5,
    float iouThreshold = 0.5
) {
 
    std::vector<Detection> finalDetections;

    // 按类别存储 box 和 score
    std::map<int, std::vector<cv::Rect>> classBoxes;
    std::map<int, std::vector<float>> classScores;

    for (int i = 0; i < detOutput.rows; ++i) {
        float objConf = detOutput.at<float>(i, 4);
        if (objConf < confThreshold) continue;

        cv::Mat scoresMat = detOutput.row(i).colRange(5, detOutput.cols);
        cv::Point classIdPoint;
        double classScore;
        cv::minMaxLoc(scoresMat, 0, &classScore, 0, &classIdPoint);

        float totalScore = objConf * static_cast<float>(classScore);
        if (totalScore < confThreshold) continue;

        int cls = classIdPoint.x;
        float cx = detOutput.at<float>(i, 0);
        float cy = detOutput.at<float>(i, 1);
        float w = detOutput.at<float>(i, 2);
        float h = detOutput.at<float>(i, 3);

        cv::Rect box = XYWH2XYXY(cx, cy, w, h, xFactor, yFactor);

        classBoxes[cls].push_back(box);
        classScores[cls].push_back(totalScore);
    }

    // 对每个类别做 NMS
    for (auto& kv : classBoxes) {
        int cls = kv.first;
        std::vector<cv::Rect>& boxes = kv.second;
        std::vector<float>& scores = classScores[cls];

        std::vector<int> keep = NMS(boxes, scores, iouThreshold);
        for (int idx : keep) {
            finalDetections.push_back({ boxes[idx], cls, scores[idx] });
        }
    }

    return finalDetections;
}

// 带优先级的 Postprocess
std::vector<Detection> PostprocessDetectionsWithPriority(
    const cv::Mat& detOutput,
    float xFactor,
    float yFactor,
    float confThreshold = 0.5,
    float iouThreshold = 0.5,
    const std::vector<int>& classPriority = { 1, 2, 0, 3 } // 类别优先级
) {
    std::vector<cv::Rect> boxes;
    std::vector<float> scores;
    std::vector<int> classIds;

    for (int i = 0; i < detOutput.rows; ++i) {
        float objConf = detOutput.at<float>(i, 4);
        if (objConf < confThreshold) continue;

        cv::Mat scoresMat = detOutput.row(i).colRange(5, detOutput.cols);
        cv::Point classIdPoint;
        double classScore;
        cv::minMaxLoc(scoresMat, 0, &classScore, 0, &classIdPoint);

        float totalScore = objConf * static_cast<float>(classScore);
        if (totalScore < confThreshold) continue;

        int cls = classIdPoint.x;
        float cx = detOutput.at<float>(i, 0);
        float cy = detOutput.at<float>(i, 1);
        float w = detOutput.at<float>(i, 2);
        float h = detOutput.at<float>(i, 3);

        cv::Rect box = XYWH2XYXY(cx, cy, w, h, xFactor, yFactor);

        boxes.push_back(box);
        scores.push_back(totalScore);
        classIds.push_back(cls);
    }

    std::vector<int> keep = NMSWithPriority(boxes, scores, classIds, classPriority, iouThreshold);

    std::vector<Detection> finalDetections;
    for (int idx : keep) {
        finalDetections.push_back({ boxes[idx], classIds[idx], scores[idx] });
    }
    return finalDetections;
}

// 过滤并合并类别3的检测框
std::vector<Detection> FilterByClass3BigBox(
    const std::vector<Detection>& detections,
    int W, int H
) {
    if (detections.empty()) return detections;

    // 找出所有类别3的框
    std::vector<cv::Rect> cls3Boxes;
    for (const auto& det : detections) {
        if (det.classId == 3) {
            cls3Boxes.push_back(det.box);
        }
    }

    if (cls3Boxes.empty()) {
        return detections; // 没有类3，直接返回
    }

    // 合并类别3的大框（取最小x1,y1和最大x2,y2）
    int bx1 = W, by1 = H, bx2 = 0, by2 = 0;
    for (const auto& b : cls3Boxes) {
        bx1 = std::min(bx1, b.x);
        by1 = std::min(by1, b.y);
        bx2 = std::max(bx2, b.x + b.width);
        by2 = std::max(by2, b.y + b.height);
    }
    bx1 = std::max(0, bx1);
    by1 = std::max(0, by1);
    bx2 = std::min(W, bx2);
    by2 = std::min(H, by2);

    cv::Rect bigBox(bx1, by1, bx2 - bx1, by2 - by1);

    // 保留中心点在大框内的所有检测
    std::vector<Detection> kept;
    for (const auto& det : detections) {
        int cx = det.box.x + det.box.width / 2;
        int cy = det.box.y + det.box.height / 2;
        if (bigBox.contains(cv::Point(cx, cy))) {
            kept.push_back(det);
        }
    }

    if (kept.empty()) {
        return detections; // 如果过滤后为空，就返回原始
    }

    // 计算所有保留框的外接矩形
    int x_min = W, y_min = H, x_max = 0, y_max = 0;
    for (const auto& det : kept) {
        x_min = std::min(x_min, det.box.x);
        y_min = std::min(y_min, det.box.y);
        x_max = std::max(x_max, det.box.x + det.box.width);
        y_max = std::max(y_max, det.box.y + det.box.height);
    }

    // 更新所有类别3框为统一大框
    for (auto& det : kept) {
        if (det.classId == 3) {
            det.box = cv::Rect(x_min, y_min, x_max - x_min, y_max - y_min);
        }
    }

    return kept;
}

// 将类别3合并为一个 
std::vector<Detection> UpdateClass3ToSingleBigBox(
    const std::vector<Detection>& detections,
    int W, int H
) {
    if (detections.empty()) return detections;

    int x_min = W, y_min = H, x_max = 0, y_max = 0;
    bool hasOther = false;

    // 统计非3类别的最大外接框
    for (const auto& det : detections) {
        if (det.classId == 3) continue;
        hasOther = true;
        x_min = std::min(x_min, det.box.x);
        y_min = std::min(y_min, det.box.y);
        x_max = std::max(x_max, det.box.x + det.box.width);
        y_max = std::max(y_max, det.box.y + det.box.height);
    }

    // 如果没有其它类别，保持原样
    if (!hasOther) return detections;

    // 限制在图像范围内
    x_min = std::max(0, x_min - 1);
    y_min = std::max(0, y_min - 1);
    x_max = std::min(W, x_max + 1);
    y_max = std::min(H, y_max + 1);

    cv::Rect bigBox(x_min, y_min, x_max - x_min, y_max - y_min);
    std::cout << bigBox << std::endl;
    std::vector<Detection> updated;
    updated.reserve(detections.size());

    bool addedClass3 = false;

    for (const auto& det : detections) {
        if (det.classId == 3) {
            if (!addedClass3) {
                updated.push_back({ bigBox, 3, det.confidence });
                addedClass3 = true;
            }
            // 其余 class=3 的框丢弃
        }
        else {
            updated.push_back(det);
        }
    }

    // 如果原本没有 class=3 框，则新增一个
    if (!addedClass3) {
        updated.push_back({ bigBox, 3, 1.0f });
    }

    return updated;
}

/**
 * @brief 将任意类型图像转换为 uint8 三通道灰度图
 *        输入可为 uint8/uint16，单通道或三通道
 * @param src 输入图像
 * @param dst 输出图像 (CV_8UC3)
 */
void ConvertToGrayUint8ThreeChannel(const cv::Mat& src, cv::Mat& dst) {
    cv::Mat tmp;

    // 1. 转换为 uint8
    if (src.depth() == CV_16U) {
        double minVal, maxVal;
        cv::minMaxLoc(src, &minVal, &maxVal);
        double scale = (maxVal > 0) ? 255.0 / maxVal : 1.0;
        src.convertTo(tmp, CV_8U, scale);
    }
    else if (src.depth() == CV_8U) {
        tmp = src;
    }
    else {
        throw std::runtime_error("Unsupported image depth! Only CV_8U or CV_16U are supported.");
    }

    // 2. 转为灰度
    cv::Mat gray;
    if (tmp.channels() == 3) {
        cv::cvtColor(tmp, gray, cv::COLOR_BGR2GRAY);
    }
    else if (tmp.channels() == 1) {
        gray = tmp;
    }
    else {
        throw std::runtime_error("Unsupported number of channels! Only 1 or 3 are supported.");
    }

    // 3. 灰度复制到三通道
    cv::cvtColor(gray, dst, cv::COLOR_GRAY2BGR);
}

YoloOnnxDetector::YoloOnnxDetector(const std::string& modelPath, bool useCuda, int numThreads, int width, int height)
    : env_(ORT_LOGGING_LEVEL_ERROR, "yolo-detector")
{
    // 检查模型文件是否存在
    std::ifstream f(modelPath);
    if (!f.good()) {
        throw std::runtime_error("模型文件不存在: " + modelPath);
    }
    f.close();

    // Session配置
    sessionOptions_.SetGraphOptimizationLevel(GraphOptimizationLevel::ORT_ENABLE_ALL); // (ORT_ENABLE_BASIC);
    sessionOptions_.SetIntraOpNumThreads(numThreads);

    // 是否使用CUDA
    auto providers = Ort::GetAvailableProviders();
    auto cudaAvailable = std::find(providers.begin(), providers.end(), "CUDAExecutionProvider");
    if (useCuda && cudaAvailable != providers.end()) {
        OrtSessionOptionsAppendExecutionProvider_CUDA(sessionOptions_, 0);
        std::cout << "使用 CUDA" << std::endl;
    }
    else {
        std::cout << "使用 CPU" << std::endl;
    }

    // 创建Session
    std::wstring wModelPath(modelPath.begin(), modelPath.end());
    session_ = Ort::Session(env_, wModelPath.c_str(), sessionOptions_);

    Ort::AllocatorWithDefaultOptions allocator;

    // 获取输入维度信息
    auto inputShape = session_.GetInputTypeInfo(0).GetTensorTypeAndShapeInfo().GetShape();

    // 判断是否动态输入
    inputDynamic_ = std::any_of(inputShape.begin(), inputShape.end(), [](int64_t dim) { return dim == -1; });
     
    if (!inputDynamic_) {
        // 固定输入，直接赋值
        inputB_ = inputShape[0];
        inputC_ = inputShape[1];
        inputH_ = inputShape[2];
        inputW_ = inputShape[3];
    }
    else {
        // 动态输入，赋默认值（batch=1，channels一般为3）
        inputB_ = inputShape[0] > 0 ? inputShape[0] : 1;
        inputC_ = inputShape[1] > 0 ? inputShape[1] : 3;
        inputH_ = 640; // 默认高，推理时用实际图像尺寸替代
        inputW_ = 640; // 默认宽，推理时用实际图像尺寸替代
    }

    // 输入输出节点名称
    auto inputName = session_.GetInputNameAllocated(0, allocator);
    inputNodeNames_.push_back(inputName.get());

    auto outputShape = session_.GetOutputTypeInfo(0).GetTensorTypeAndShapeInfo().GetShape();
    numPredictions_ = outputShape[1];
    numAttributes_ = outputShape[2];

    auto outputName = session_.GetOutputNameAllocated(0, allocator);
    outputNodeNames_.push_back(outputName.get());

}

void YoloOnnxDetector::LoadLabels(const std::vector<std::string>& labels) {
    labels_ = labels;
}

cv::Mat YoloOnnxDetector::Preprocess(const cv::Mat& origin_image, float& xFactor, float& yFactor) {
    
    cv::Mat image(origin_image);
    ConvertToGrayUint8ThreeChannel(origin_image, image);

    int srcW = image.cols;
    int srcH = image.rows;
 
    if (inputDynamic_) {
        // 动态输入：不resize，直接用原图尺寸
        inputW_ = find_best_scale(srcW);
        inputH_ = find_best_scale(srcH);
 
        xFactor = static_cast<float>(srcW) / inputW_;
        yFactor = static_cast<float>(srcH) / inputH_;

        return cv::dnn::blobFromImage(image, 1.0 / 255.0, cv::Size(inputW_, inputH_), cv::Scalar(0, 0, 0), true, false);
    }
    else {
        // 固定输入：resize到模型输入大小
        cv::Mat resized;
        cv::resize(image, resized, cv::Size(inputW_, inputH_));

        // 计算比例（原图尺寸 / 模型输入尺寸）
        xFactor = static_cast<float>(srcW) / inputW_;
        yFactor = static_cast<float>(srcH) / inputH_;

        return cv::dnn::blobFromImage(resized, 1.0 / 255.0, cv::Size(inputW_, inputH_), cv::Scalar(0, 0, 0), true, false);

    }
}

void YoloOnnxDetector::DrawDetections(cv::Mat& image, const std::vector<Detection>& detections) {
    std::vector<cv::Scalar> color = {
        {0,0,255},
        {0,255,0},
        {255,0,0 },
        {255,255,255},
    };
    for (const auto& det : detections) {
        cv::rectangle(image, det.box, color[det.classId], 1);
    }
}


std::vector<Detection> YoloOnnxDetector::Detect(const cv::Mat& image) {
    float xFactor = 1.0f, yFactor = 1.0f;
    cv::Mat blob = Preprocess(image, xFactor, yFactor);
 
    auto type_info = session_.GetInputTypeInfo(0);
    auto tensor_info = type_info.GetTensorTypeAndShapeInfo();
    auto input_shape = tensor_info.GetShape();
    

    // 构造输入tensor形状
    std::array<int64_t, 4> inputShape;
    inputShape = { inputB_, inputC_, inputH_, inputW_ };
   
    auto memInfo = Ort::MemoryInfo::CreateCpu(OrtDeviceAllocator, OrtMemTypeCPU);
    Ort::Value inputTensor = Ort::Value::CreateTensor<float>(
        memInfo, blob.ptr<float>(), blob.total(), inputShape.data(), inputShape.size()
    );

    std::vector<const char*> inNames, outNames;
    for (auto& s : inputNodeNames_) inNames.push_back(s.c_str());
    for (auto& s : outputNodeNames_) outNames.push_back(s.c_str());

    auto start = std::chrono::high_resolution_clock::now();

    auto outputTensors = session_.Run(Ort::RunOptions{ nullptr },
        inNames.data(), &inputTensor, 1,
        outNames.data(), outNames.size());


    float* outputData = outputTensors[0].GetTensorMutableData<float>();
    auto outShape = outputTensors[0].GetTensorTypeAndShapeInfo().GetShape();
    size_t total = outputTensors[0].GetTensorTypeAndShapeInfo().GetElementCount();

    auto outputShape = outputTensors[0].GetTensorTypeAndShapeInfo().GetShape();
    numPredictions_ = outputShape[1];
    numAttributes_ = outputShape[2];

  
    auto end = std::chrono::high_resolution_clock::now();
    std::cout << "推理耗时: "
        << std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count()
        << " ms\n";

    // 结果转换
    const float* pdata = outputTensors[0].GetTensorMutableData<float>();
    cv::Mat detOutput(numPredictions_, numAttributes_, CV_32F, (void*)pdata);

    std::vector<Detection> detections = PostprocessDetectionsWithPriority(detOutput, xFactor, yFactor, confThreshold_, nmsThreshold_);
 
    detections = UpdateClass3ToSingleBigBox(detections, image.cols, image.rows);
     
    return detections;
}

std::vector<std::vector<Detection>> YoloOnnxDetector::DetectBatch(
    const std::vector<cv::Mat>& images)
{
    int batchSize = static_cast<int>(images.size());
    CV_Assert(batchSize > 0);

    std::vector<float> inputTensorValues;
    inputTensorValues.reserve(batchSize * inputC_ * inputH_ * inputW_);

    std::vector<float> xFactors(batchSize), yFactors(batchSize);

    // 预处理所有图片并拼接
    for (int i = 0; i < batchSize; ++i) {
        cv::Mat blob = Preprocess(images[i], xFactors[i], yFactors[i]);

        CV_Assert(blob.isContinuous() && blob.type() == CV_32F);
        inputTensorValues.insert(
            inputTensorValues.end(), (float*)blob.datastart, (float*)blob.dataend
        );
    }

    // 构造输入 tensor
    std::array<int64_t, 4> inputShape = { batchSize, inputC_, inputH_, inputW_ };
    auto memInfo = Ort::MemoryInfo::CreateCpu(OrtDeviceAllocator, OrtMemTypeCPU);
    Ort::Value inputTensor = Ort::Value::CreateTensor<float>(
        memInfo, 
        inputTensorValues.data(), 
        inputTensorValues.size(),
        inputShape.data(), 
        inputShape.size()
    );

    std::vector<const char*> inNames, outNames;
    for (auto& s : inputNodeNames_) inNames.push_back(s.c_str());
    for (auto& s : outputNodeNames_) outNames.push_back(s.c_str());

    auto start = std::chrono::high_resolution_clock::now();
    auto outputTensors = session_.Run(Ort::RunOptions{ nullptr },
        inNames.data(), &inputTensor, 1,
        outNames.data(), outNames.size());
    auto end = std::chrono::high_resolution_clock::now();

    std::cout << "Batch推理耗时: "
        << std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count()
        << " ms\n";

    // 输出解析
    auto outShape = outputTensors[0].GetTensorTypeAndShapeInfo().GetShape();
    // outShape = [batch_size, numPreds, numAttrs]
    int numPreds = outShape[1];
    int numAttrs = outShape[2];

    const float* pdata = outputTensors[0].GetTensorMutableData<float>();

    std::vector<std::vector<Detection>> batchDetections(batchSize);

    for (int b = 0; b < batchSize; ++b) {
        // 每个 batch 的输出起始位置
        const float* batchData = pdata + b * numPreds * numAttrs;

        cv::Mat detOutput(numPreds, numAttrs, CV_32F, (void*)batchData);

        std::vector<Detection> detections = PostprocessDetectionsWithPriority(detOutput, xFactors[b], yFactors[b],  confThreshold_, nmsThreshold_);

        detections = UpdateClass3ToSingleBigBox(detections, images[b].cols, images[b].rows);

        batchDetections[b] = std::move(detections);
    }

    return batchDetections;
}
