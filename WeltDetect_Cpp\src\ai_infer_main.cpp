#include "Detector.h"
#include <iostream>
#include <filesystem>
 
int main() {
	try {
		
		YoloOnnxDetector detector("D:/work/Code/OnnxYolo-main/OnnxYolo-main/x64/Debug/Config/bestV0.onnx", false, 2);

		// 预留
		detector.LoadLabels({"0"}); 

		cv::Mat img = cv::imread("D:/work/Code/OnnxYolo-main/OnnxYolo-main/x64/Debug/1.jpg");
		if (img.empty()) {
			std::cerr << "读取图片失败\n";
			return -1;
		}

		cv::Mat result;
		std::vector<Detection> detections = detector.Detect(img);

		/*
		
		struct Detection {
			cv::Rect box;      // box.x = center x  box.y = center y  box.width = w  box.height = h 
			int classId;
			float confidence;
		};
		
		*/

		result = img.clone();
		detector.DrawDetections( result, detections); // 画图

		cv::imshow("Result", result);
		cv::imwrite("./output.jpg", result);
		cv::waitKey(0);
	}
	catch (const std::exception& ex) {
		std::cerr << "推理错误: " << ex.what() << std::endl;
		return -1;
	}
	return 0;
}
