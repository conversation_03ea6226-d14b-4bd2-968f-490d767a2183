#!/usr/bin/env python3
"""
Compare comprehensive results between Python and C++ implementations
Both using bestV5 models (bestV5.pt vs bestV5.onnx)
"""

import subprocess
import re
import sys
import os

def run_python_autofocus():
    """Run Python version and extract auto-focus results"""
    print("Running Python version in sphereSeg environment...")

    os.chdir("WeltDetect")
    try:
        # Use conda run to execute in sphereSeg environment
        result = subprocess.run([
            "conda", "run", "-n", "sphereSeg",
            "python", "main.py",
            "--input", "./data/processed/Slice_t"
        ],
        capture_output=True,
        text=True,
        encoding='utf-8',
        errors='ignore',
        env={**os.environ, "KMP_DUPLICATE_LIB_OK": "TRUE"}
        )
        
        output = result.stdout
        print("Python output (relevant lines):")
        
        # Extract comprehensive information
        focus_peaks = None
        target_layer = None
        detection_count = None
        sphere_count = None
        plane_coeffs = None
        z_list = None

        for line in output.split('\n'):
            if "对焦峰索引:" in line:
                # Extract peak indices
                match = re.search(r'对焦峰索引: \[(.*?)\]', line)
                if match:
                    focus_peaks = [int(x.strip()) for x in match.group(1).split(',')]
                print(f"  {line.strip()}")
            elif "最终目标层:" in line:
                # Extract target layer and detection count
                match = re.search(r'最终目标层: (\d+), 共(\d+)个目标', line)
                if match:
                    target_layer = int(match.group(1))
                    detection_count = int(match.group(2))
                print(f"  {line.strip()}")
            elif "拟合平面:" in line:
                # Extract plane coefficients
                match = re.search(r'拟合平面: z = ([-\d.]+) x \+ ([-\d.]+) y \+ ([-\d.]+)', line)
                if match:
                    plane_coeffs = (float(match.group(1)), float(match.group(2)), float(match.group(3)))
                print(f"  {line.strip()}")
            elif "所有检测框的z层号：" in line:
                print(f"  {line.strip()}")
                # Next line should contain the z list
            elif line.strip().startswith('[') and ',' in line and line.strip().endswith(']'):
                # Extract z list
                z_str = line.strip()[1:-1]  # Remove brackets
                try:
                    z_list = [int(x.strip()) for x in z_str.split(',')]
                    print(f"  Found {len(z_list)} z values")
                except:
                    pass
            elif "成功分割" in line and "个球体" in line:
                # Extract sphere count
                match = re.search(r'成功分割 (\d+) 个球体', line)
                if match:
                    sphere_count = int(match.group(1))
                print(f"  {line.strip()}")

        return focus_peaks, target_layer, detection_count, sphere_count, plane_coeffs, z_list

    except Exception as e:
        print(f"Error running Python version: {e}")
        return None, None, None, None, None, None
    finally:
        os.chdir("..")

def run_cpp_autofocus():
    """Run C++ version and extract auto-focus results"""
    print("\nRunning C++ version...")
    
    os.chdir("SphereSeg3D")
    try:
        result = subprocess.run([
            ".\\x64\\Release\\SphereSeg3DApp.exe",
            ".\\data\\processed\\Slice_t"
        ],
        capture_output=True,
        text=True,
        encoding='utf-8',
        errors='ignore'
        )
        
        output = result.stdout
        print("C++ output:")
        print(output)
        
        # Extract comprehensive information
        focus_peaks = None
        target_layer = None
        detection_count = None
        plane_coeffs = None
        z_list = None

        for line in output.split('\n'):
            if "对焦峰索引:" in line:
                # Extract peak indices
                match = re.search(r'对焦峰索引: \[(.*?)\]', line)
                if match:
                    focus_peaks = [int(x.strip()) for x in match.group(1).split(',')]
            elif "Focus slice z=" in line:
                # Extract target layer
                match = re.search(r'Focus slice z=(\d+)', line)
                if match:
                    target_layer = int(match.group(1))
            elif "最终目标层:" in line:
                # Extract final target layer and detection count
                match = re.search(r'最终目标层: (\d+), 共(\d+)个目标', line)
                if match:
                    target_layer = int(match.group(1))
                    detection_count = int(match.group(2))
            elif "拟合平面:" in line:
                # Extract plane coefficients
                match = re.search(r'拟合平面: z = ([-\d.]+) x \+ ([-\d.]+) y \+ ([-\d.]+)', line)
                if match:
                    plane_coeffs = (float(match.group(1)), float(match.group(2)), float(match.group(3)))
            elif "所有检测框的z层号：" in line:
                # Next line should contain the z list
                pass
            elif line.strip().startswith('[') and ',' in line and line.strip().endswith(']'):
                # Extract z list
                z_str = line.strip()[1:-1]  # Remove brackets
                try:
                    z_list = [int(x.strip()) for x in z_str.split(',')]
                except:
                    pass

        return focus_peaks, target_layer, detection_count, None, plane_coeffs, z_list

    except Exception as e:
        print(f"Error running C++ version: {e}")
        return None, None, None, None, None, None
    finally:
        os.chdir("..")

def compare_results(py_peaks, py_target, py_dets, py_spheres, py_plane, py_z_list,
                   cpp_peaks, cpp_target, cpp_dets, cpp_spheres, cpp_plane, cpp_z_list):
    """Compare Python and C++ comprehensive results"""
    print("\n" + "="*80)
    print("COMPREHENSIVE COMPARISON RESULTS (bestV5.pt vs bestV5.onnx)")
    print("="*80)

    print(f"Python - Focus peaks: {py_peaks}, Target layer: {py_target}")
    print(f"         Detections: {py_dets}, Spheres: {py_spheres}")
    print(f"         Plane coeffs: {py_plane}")
    print(f"         Z-list length: {len(py_z_list) if py_z_list else 'None'}")

    print(f"C++    - Focus peaks: {cpp_peaks}, Target layer: {cpp_target}")
    print(f"         Detections: {cpp_dets}, Spheres: {cpp_spheres}")
    print(f"         Plane coeffs: {cpp_plane}")
    print(f"         Z-list length: {len(cpp_z_list) if cpp_z_list else 'None'}")

    success_count = 0
    total_tests = 6

    # Compare target layers
    if py_target == cpp_target:
        print("✅ Target layers match!")
        success_count += 1
    else:
        print("❌ Target layers differ!")

    # Compare focus peaks
    if py_peaks == cpp_peaks:
        print("✅ Focus peaks match!")
        success_count += 1
    else:
        print("❌ Focus peaks differ!")
        if py_peaks and cpp_peaks:
            common_peaks = set(py_peaks) & set(cpp_peaks)
            if common_peaks:
                print(f"   Common peaks: {sorted(common_peaks)}")

    # Compare detection counts (allow some tolerance)
    if py_dets and cpp_dets:
        det_diff = abs(py_dets - cpp_dets)
        det_ratio = det_diff / max(py_dets, cpp_dets)
        if det_ratio <= 0.1:  # Allow 10% difference
            print(f"✅ Detection counts similar (diff: {det_diff}, {det_ratio:.1%})")
            success_count += 1
        else:
            print(f"⚠️  Detection counts differ significantly (diff: {det_diff}, {det_ratio:.1%})")
    else:
        print("❓ Detection count comparison unavailable")

    # Compare plane coefficients
    if py_plane and cpp_plane:
        a_diff = abs(py_plane[0] - cpp_plane[0])
        b_diff = abs(py_plane[1] - cpp_plane[1])
        c_diff = abs(py_plane[2] - cpp_plane[2])
        if a_diff < 0.01 and b_diff < 0.01 and c_diff < 0.5:
            print("✅ Plane coefficients match!")
            success_count += 1
        else:
            print(f"⚠️  Plane coefficients differ (a:{a_diff:.4f}, b:{b_diff:.4f}, c:{c_diff:.2f})")
    else:
        print("❓ Plane coefficient comparison unavailable")

    # Compare z-list lengths
    if py_z_list and cpp_z_list:
        len_diff = abs(len(py_z_list) - len(cpp_z_list))
        if len_diff == 0:
            print("✅ Z-list lengths match!")
            success_count += 1
        else:
            print(f"⚠️  Z-list lengths differ (diff: {len_diff})")
    else:
        print("❓ Z-list comparison unavailable")

    # Compare sphere counts
    if py_spheres and cpp_spheres:
        sphere_diff = abs(py_spheres - cpp_spheres) if cpp_spheres else py_spheres
        if sphere_diff == 0:
            print("✅ Sphere counts match!")
            success_count += 1
        else:
            print(f"⚠️  Sphere counts differ (diff: {sphere_diff})")
    elif py_spheres:
        print("❓ C++ sphere count unavailable (detection-only mode)")
        total_tests = 5  # Adjust total since C++ didn't do segmentation
    else:
        print("❓ Sphere count comparison unavailable")

    # Overall assessment
    success_rate = success_count / total_tests
    print(f"\nSuccess Rate: {success_count}/{total_tests} ({success_rate:.1%})")

    if success_rate >= 0.75:
        print("🎉 MIGRATION STATUS: SUCCESS!")
        print("   Core functionality is working correctly.")
        return True
    elif success_rate >= 0.5:
        print("⚠️  MIGRATION STATUS: PARTIAL SUCCESS")
        print("   Most functionality working, some differences found.")
        return False
    else:
        print("❌ MIGRATION STATUS: NEEDS ATTENTION")
        print("   Significant differences found, review required.")
        return False

def main():
    print("SphereSeg3D Migration Comparison Test (bestV5 models)")
    print("="*80)

    # Run both versions
    py_peaks, py_target, py_dets, py_spheres, py_plane, py_z_list = run_python_autofocus()
    cpp_peaks, cpp_target, cpp_dets, cpp_spheres, cpp_plane, cpp_z_list = run_cpp_autofocus()

    # Compare results
    if py_peaks is not None and cpp_peaks is not None:
        success = compare_results(py_peaks, py_target, py_dets, py_spheres, py_plane, py_z_list,
                                cpp_peaks, cpp_target, cpp_dets, cpp_spheres, cpp_plane, cpp_z_list)
        sys.exit(0 if success else 1)
    else:
        print("❌ Failed to extract results from one or both implementations")
        sys.exit(1)

if __name__ == "__main__":
    main()
