#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import subprocess
import sys

def run_python_version():
    """运行Python版本并捕获输出"""
    try:
        os.chdir("WeltDetect")
        result = subprocess.run([
            "conda", "run", "-n", "sphereSeg", 
            "python", "main.py", "--input", "./data/processed/Slice_t"
        ], 
        capture_output=True,
        text=True,
        encoding='utf-8',
        errors='ignore',
        env={**os.environ, "KMP_DUPLICATE_LIB_OK": "TRUE"}
        )
        
        output = result.stdout
        print("Python version output:")
        
        # 提取关键信息
        focus_peaks = None
        target_layer = None
        detection_count = None
        sphere_count = None
        plane_coeffs = None
        corner_samples = None
        z_list = None

        for line in output.split('\n'):
            if "对焦峰索引:" in line:
                match = re.search(r'对焦峰索引: \[(.*?)\]', line)
                if match:
                    focus_peaks = [int(x.strip()) for x in match.group(1).split(',')]
                print(f"  {line.strip()}")
            elif "最终目标层:" in line:
                match = re.search(r'最终目标层: (\d+), 共(\d+)个目标', line)
                if match:
                    target_layer = int(match.group(1))
                    detection_count = int(match.group(2))
                print(f"  {line.strip()}")
            elif "角点拟合样本:" in line:
                match = re.search(r'角点拟合样本: \[(.*?)\]', line)
                if match:
                    corner_samples = match.group(1)
                print(f"  {line.strip()}")
            elif "拟合平面:" in line:
                match = re.search(r'拟合平面: z = ([-\d.]+) x \+ ([-\d.]+) y \+ ([-\d.]+)', line)
                if match:
                    plane_coeffs = (float(match.group(1)), float(match.group(2)), float(match.group(3)))
                print(f"  {line.strip()}")
            elif "所有检测框的z层号：" in line:
                print(f"  {line.strip()}")
            elif line.strip().startswith('[') and ',' in line and line.strip().endswith(']'):
                z_str = line.strip()[1:-1]
                try:
                    z_list = [int(x.strip()) for x in z_str.split(',')]
                    print(f"  找到 {len(z_list)} 个z值")
                except:
                    pass
            elif "成功分割" in line and "个球体" in line:
                match = re.search(r'成功分割 (\d+) 个球体', line)
                if match:
                    sphere_count = int(match.group(1))
                print(f"  {line.strip()}")

        return focus_peaks, target_layer, detection_count, sphere_count, plane_coeffs, corner_samples, z_list

    except Exception as e:
        print(f"运行Python版本时出错: {e}")
        return None, None, None, None, None, None, None
    finally:
        os.chdir("..")

def run_cpp_version():
    """运行C++版本并捕获输出"""
    try:
        os.chdir("SphereSeg3D")
        
        # 删除之前的输出
        subprocess.run(["Remove-Item", "output\\processed\\Slice_t\\*", "-Force"], 
                      shell=True, capture_output=True)
        
        # 运行C++版本
        result = subprocess.run([
            ".\\x64\\Release\\SphereSeg3DApp.exe", 
            ".\\data\\processed\\Slice_t"
        ], 
        capture_output=True,
        text=True,
        encoding='utf-8',
        errors='ignore'
        )
        
        output = result.stdout
        print("\nC++版本输出:")
        
        # 提取关键信息
        focus_peaks = None
        target_layer = None
        detection_count = None
        sphere_count = None
        plane_coeffs = None
        corner_samples = None
        z_list = None

        for line in output.split('\n'):
            if "对焦峰索引:" in line or "Focus peaks:" in line:
                if "对焦峰索引:" in line:
                    match = re.search(r'对焦峰索引: \[(.*?)\]', line)
                else:
                    match = re.search(r'Focus peaks: \[(.*?)\]', line)
                if match:
                    focus_peaks = [int(x.strip()) for x in match.group(1).split(',')]
                print(f"  {line.strip()}")
            elif "最终目标层:" in line or "Final target layer:" in line:
                if "最终目标层:" in line:
                    match = re.search(r'最终目标层: (\d+), 共(\d+)个目标', line)
                else:
                    match = re.search(r'Final target layer: (\d+), total (\d+) targets', line)
                if match:
                    target_layer = int(match.group(1))
                    detection_count = int(match.group(2))
                print(f"  {line.strip()}")
            elif "角点拟合样本:" in line:
                match = re.search(r'角点拟合样本: \[(.*?)\]', line)
                if match:
                    corner_samples = match.group(1)
                print(f"  {line.strip()}")
            elif "拟合平面:" in line or "Tilt plane:" in line:
                if "拟合平面:" in line:
                    match = re.search(r'拟合平面: z = ([-\d.]+) x \+ ([-\d.]+) y \+ ([-\d.]+)', line)
                else:
                    match = re.search(r'Tilt plane: z = ([-\d.]+) x \+ ([-\d.]+) y \+ ([-\d.]+)', line)
                if match:
                    plane_coeffs = (float(match.group(1)), float(match.group(2)), float(match.group(3)))
                print(f"  {line.strip()}")
            elif "所有检测框的z层号：" in line or "All detection z layers:" in line:
                print(f"  {line.strip()}")
            elif line.strip().startswith('[') and ',' in line and line.strip().endswith(']'):
                z_str = line.strip()[1:-1]
                try:
                    z_list = [int(x.strip()) for x in z_str.split(',')]
                    print(f"  找到 {len(z_list)} 个z值")
                except:
                    pass
            elif "成功分割" in line and "个球体" in line:
                match = re.search(r'成功分割 (\d+) 个球体', line)
                if match:
                    sphere_count = int(match.group(1))
                print(f"  {line.strip()}")

        return focus_peaks, target_layer, detection_count, sphere_count, plane_coeffs, corner_samples, z_list

    except Exception as e:
        print(f"运行C++版本时出错: {e}")
        return None, None, None, None, None, None, None
    finally:
        os.chdir("..")

def compare_results():
    """对比两个版本的结果"""
    print("=" * 60)
    print("SphereSeg3D Python vs C++ 详细对比")
    print("=" * 60)
    
    # 运行两个版本
    py_results = run_python_version()
    cpp_results = run_cpp_version()
    
    py_peaks, py_target, py_det_count, py_sphere_count, py_plane, py_corners, py_z_list = py_results
    cpp_peaks, cpp_target, cpp_det_count, cpp_sphere_count, cpp_plane, cpp_corners, cpp_z_list = cpp_results
    
    print("\n" + "=" * 60)
    print("详细对比结果")
    print("=" * 60)
    
    success_count = 0
    total_checks = 6
    
    # 对比对焦峰索引
    if py_peaks and cpp_peaks:
        if py_peaks == cpp_peaks:
            print("✅ 对焦峰索引完全一致!")
            success_count += 1
        else:
            print(f"⚠️  对焦峰索引不一致: Python={py_peaks}, C++={cpp_peaks}")
    else:
        print("❓ 对焦峰索引对比不可用")
    
    # 对比目标层
    if py_target is not None and cpp_target is not None:
        if py_target == cpp_target:
            print("✅ 目标层完全一致!")
            success_count += 1
        else:
            print(f"⚠️  目标层不一致: Python={py_target}, C++={cpp_target}")
    else:
        print("❓ 目标层对比不可用")
    
    # 对比检测数量
    if py_det_count is not None and cpp_det_count is not None:
        if py_det_count == cpp_det_count:
            print("✅ 检测数量完全一致!")
            success_count += 1
        else:
            print(f"⚠️  检测数量不一致: Python={py_det_count}, C++={cpp_det_count}")
    else:
        print("❓ 检测数量对比不可用")
    
    # 对比角点拟合样本
    if py_corners and cpp_corners:
        if py_corners == cpp_corners:
            print("✅ 角点拟合样本完全一致!")
            success_count += 1
        else:
            print(f"⚠️  角点拟合样本不一致:")
            print(f"    Python: {py_corners}")
            print(f"    C++:    {cpp_corners}")
    else:
        print("❓ 角点拟合样本对比不可用")
    
    # 对比平面系数
    if py_plane and cpp_plane:
        a_diff = abs(py_plane[0] - cpp_plane[0])
        b_diff = abs(py_plane[1] - cpp_plane[1])
        c_diff = abs(py_plane[2] - cpp_plane[2])
        if a_diff < 0.01 and b_diff < 0.01 and c_diff < 0.5:
            print("✅ 平面系数基本一致!")
            success_count += 1
        else:
            print(f"⚠️  平面系数差异较大:")
            print(f"    Python: z = {py_plane[0]:.4f} x + {py_plane[1]:.4f} y + {py_plane[2]:.2f}")
            print(f"    C++:    z = {cpp_plane[0]:.4f} x + {cpp_plane[1]:.4f} y + {cpp_plane[2]:.2f}")
            print(f"    差异:   a={a_diff:.4f}, b={b_diff:.4f}, c={c_diff:.2f}")
    else:
        print("❓ 平面系数对比不可用")
    
    # 对比z层分布
    if py_z_list and cpp_z_list:
        len_diff = abs(len(py_z_list) - len(cpp_z_list))
        if len_diff == 0:
            # 检查z值分布
            py_z_set = set(py_z_list)
            cpp_z_set = set(cpp_z_list)
            if py_z_set == cpp_z_set:
                print("✅ z层分布完全一致!")
                success_count += 1
            else:
                print(f"⚠️  z层分布不一致:")
                print(f"    Python z值范围: {min(py_z_list)}-{max(py_z_list)}, 唯一值: {sorted(py_z_set)}")
                print(f"    C++ z值范围:    {min(cpp_z_list)}-{max(cpp_z_list)}, 唯一值: {sorted(cpp_z_set)}")
        else:
            print(f"⚠️  z层数量不一致: Python={len(py_z_list)}, C++={len(cpp_z_list)}")
    else:
        print("❓ z层分布对比不可用")
    
    print("\n" + "=" * 60)
    print(f"总体评估: {success_count}/{total_checks} 项检查通过")
    if success_count == total_checks:
        print("🎉 完美！两个版本完全一致！")
    elif success_count >= total_checks * 0.8:
        print("✅ 很好！两个版本基本一致，只有微小差异")
    elif success_count >= total_checks * 0.6:
        print("⚠️  一般，两个版本有一些差异需要调整")
    else:
        print("❌ 两个版本差异较大，需要进一步调试")
    print("=" * 60)

if __name__ == "__main__":
    compare_results()
