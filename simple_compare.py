#!/usr/bin/env python3

import os

def count_detections(file_path):
    """Count non-empty lines in detection file"""
    try:
        with open(file_path, 'r') as f:
            lines = [line.strip() for line in f.readlines() if line.strip()]
        return len(lines)
    except:
        return 0

def main():
    print("=== SphereSeg3D Comparison ===")
    
    # Check Python results
    py_file = "WeltDetect/output/processed/Slice_t/focus_67_dets.txt"
    py_count = count_detections(py_file)
    print(f"Python detections: {py_count}")
    
    # Check C++ results  
    cpp_file = "SphereSeg3D/output/processed/Slice_t/focus_67_dets.txt"
    cpp_count = count_detections(cpp_file)
    print(f"C++ detections: {cpp_count}")
    
    # Compare
    if py_count == cpp_count:
        print("✅ Detection counts match!")
    else:
        print(f"⚠️ Detection counts differ by {abs(py_count - cpp_count)}")
    
    # Check if files exist
    py_exists = os.path.exists(py_file)
    cpp_exists = os.path.exists(cpp_file)
    
    print(f"Python output exists: {py_exists}")
    print(f"C++ output exists: {cpp_exists}")
    
    if py_exists and cpp_exists:
        print("Both versions generated output files successfully!")
    
if __name__ == "__main__":
    main()
