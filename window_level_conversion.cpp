// Windows(CR LF), UTF-8
#include <algorithm>
#include <cmath>

/**
 * 将16位CT值通过窗宽窗位转换到[0,1]范围
 * @param ctValue 原始CT值（16位）
 * @param windowWidth 窗宽（WW）
 * @param windowLevel 窗位（WL）
 * @return 转换后的[0,1]范围值
 */
double convertCtValueToNormalized(int16_t ctValue, double windowWidth, double windowLevel)
{
    // 计算窗口的上下边界
    double lowerBound = windowLevel - windowWidth / 2.0;
    double upperBound = windowLevel + windowWidth / 2.0;
    
    // 线性映射到[0,1]
    if (ctValue <= lowerBound)
    {
        return 0.0;
    }
    else if (ctValue >= upperBound)
    {
        return 1.0;
    }
    else
    {
        return (ctValue - lowerBound) / windowWidth;
    }
}

/**
 * 批量转换CT体数据
 * @param inputData 输入的16位CT数据
 * @param outputData 输出的[0,1]范围数据
 * @param width 图像宽度
 * @param height 图像高度
 * @param depth 图像深度
 * @param windowWidth 窗宽
 * @param windowLevel 窗位
 */
void convertVolumeToNormalized(const int16_t* inputData, 
                              double* outputData,
                              int width, int height, int depth,
                              double windowWidth, double windowLevel)
{
    size_t totalVoxels = static_cast<size_t>(width) * height * depth;
    
    for (size_t i = 0; i < totalVoxels; ++i)
    {
        outputData[i] = convertCtValueToNormalized(inputData[i], windowWidth, windowLevel);
    }
}

/**
 * 自动计算适合[0,1]范围的新窗宽窗位
 * @param originalWW 原始窗宽（16位范围）
 * @param originalWL 原始窗位（16位范围）
 * @param newWW 输出：新窗宽（[0,1]范围）
 * @param newWL 输出：新窗位（[0,1]范围）
 * @param minCtValue CT数据的最小值
 * @param maxCtValue CT数据的最大值
 */
void calculateNormalizedWindowLevel(double originalWW, double originalWL,
                                   double& newWW, double& newWL,
                                   int16_t minCtValue = -1024, int16_t maxCtValue = 3071)
{
    // CT值的总范围
    double ctRange = maxCtValue - minCtValue;
    
    // 将原始窗宽窗位映射到[0,1]
    newWW = originalWW / ctRange;
    newWL = (originalWL - minCtValue) / ctRange;
    
    // 确保在合理范围内
    newWW = std::max(0.001, std::min(1.0, newWW));
    newWL = std::max(0.0, std::min(1.0, newWL));
}

/**
 * 示例：常见的CT窗宽窗位设置
 */
struct WindowLevelPreset
{
    const char* name;
    double windowWidth;
    double windowLevel;
};

// 常见的CT窗宽窗位预设（16位范围）
const WindowLevelPreset CT_PRESETS[] = {
    {"Lung",        1500, -600},   // 肺窗
    {"Mediastinum", 350,  50},     // 纵隔窗  
    {"Bone",        1500, 300},    // 骨窗
    {"Brain",       80,   40},     // 脑窗
    {"Abdomen",     400,  50},     // 腹部窗
    {"Soft Tissue", 400,  40}      // 软组织窗
};

/**
 * 获取预设的标准化窗宽窗位
 * @param presetName 预设名称
 * @param normalizedWW 输出：标准化窗宽
 * @param normalizedWL 输出：标准化窗位
 * @return 是否找到预设
 */
bool getNormalizedPreset(const char* presetName, double& normalizedWW, double& normalizedWL)
{
    for (const auto& preset : CT_PRESETS)
    {
        if (strcmp(preset.name, presetName) == 0)
        {
            calculateNormalizedWindowLevel(preset.windowWidth, preset.windowLevel,
                                         normalizedWW, normalizedWL);
            return true;
        }
    }
    return false;
}

// 使用示例
void exampleUsage()
{
    // 原始16位窗宽窗位
    double originalWW = 400;  // 窗宽
    double originalWL = 50;   // 窗位
    
    // 转换到[0,1]范围
    double normalizedWW, normalizedWL;
    calculateNormalizedWindowLevel(originalWW, originalWL, normalizedWW, normalizedWL);
    
    // 输出结果
    printf("原始窗宽窗位: WW=%.0f, WL=%.0f\n", originalWW, originalWL);
    printf("标准化窗宽窗位: WW=%.4f, WL=%.4f\n", normalizedWW, normalizedWL);
}
